﻿<Window x:Class="CheckPatientInfo.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CheckPatientInfo"
        mc:Ignorable="d"
        Title="影像系统入室核对"
        Height="540"
        Width="720"
        WindowStyle="None"
        ResizeMode="CanMinimize"
        WindowStartupLocation="CenterScreen"
        Background="Transparent"
        AllowsTransparency="True"
        Topmost="True"
        TextOptions.TextFormattingMode="Display"
        TextOptions.TextRenderingMode="ClearType"
        UseLayoutRounding="True"
        SnapsToDevicePixels="True">

    <!-- 自定义窗口边框和内容 -->
    <Border Background="White"
            CornerRadius="12"
            BorderBrush="#E0E0E0"
            BorderThickness="1">
        <Border.Effect>
            <DropShadowEffect Color="Black"
                              Direction="270"
                              ShadowDepth="5"
                              Opacity="0.2"
                              BlurRadius="15"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="40"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 自定义标题栏 -->
            <Border Grid.Row="0"
                    Background="White"
                    CornerRadius="12,12,0,0"
                    MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0"
                               Text="影像系统入室核对"
                               Foreground="#2C3E50"
                               FontSize="14"
                               FontWeight="Bold"
                               VerticalAlignment="Center"
                               HorizontalAlignment="Left"
                               Margin="15,0,0,0"/>

                    <!-- 窗口控制按钮 -->
                    <StackPanel Grid.Column="1"
                                Orientation="Horizontal">
                        <Button Name="MinimizeButton"
                                Content="—"
                                Width="30"
                                Height="30"
                                Background="Transparent"
                                Foreground="#2C3E50"
                                BorderThickness="0"
                                FontSize="12"
                                Click="MinimizeButton_Click"
                                Style="{StaticResource MinimizeButtonStyle}"/>
                        <Button Name="CloseButton"
                                Content="✕"
                                Width="30"
                                Height="30"
                                Background="Transparent"
                                Foreground="#2C3E50"
                                BorderThickness="0"
                                FontSize="12"
                                Click="CloseButton_Click"
                                Style="{StaticResource CloseButtonStyle}"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- 主要内容区域 -->
            <Border Grid.Row="1" Margin="20" Background="White">

                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 主要内容区域 -->
                    <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="30"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 患者信息显示区域 -->
                    <Border Grid.Column="0"
                            Background="#F8F9FA"
                            CornerRadius="8"
                            Padding="16"
                            BorderBrush="#3498DB"
                            BorderThickness="4,0,0,0"
                            Height="400"
                            VerticalAlignment="Top">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" Opacity="0.1" BlurRadius="8"/>
                        </Border.Effect>

                        <StackPanel TextOptions.TextFormattingMode="Display"
                                    TextOptions.TextRenderingMode="ClearType"
                                    UseLayoutRounding="True"
                                    SnapsToDevicePixels="True">
                            <TextBlock Text="患者信息（门诊CT）"
                                       FontSize="16"
                                       FontWeight="Bold"
                                       Foreground="#2C3E50"
                                       Margin="0,0,0,15"/>

                            <!-- 患者基本信息 -->
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="姓名:" FontWeight="Bold" Foreground="#34495E" FontSize="14" VerticalAlignment="Center"/>
                                <TextBlock Grid.Column="1" Name="PatientNameText" Text="张三" Foreground="#2C3E50" FontSize="14" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                                    <Rectangle Grid.ColumnSpan="2" Height="1" Fill="#E9ECEF" VerticalAlignment="Bottom" Margin="0,35,0,0"/>
                            </Grid>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="性别:" FontWeight="Bold" Foreground="#34495E" FontSize="14" VerticalAlignment="Center"/>
                                <TextBlock Grid.Column="1" Name="PatientGenderText" Text="男" Foreground="#2C3E50" FontSize="14" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                                    <Rectangle Grid.ColumnSpan="2" Height="1" Fill="#E9ECEF" VerticalAlignment="Bottom" Margin="0,35,0,0"/>
                            </Grid>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="年龄:" FontWeight="Bold" Foreground="#34495E" FontSize="14" VerticalAlignment="Center"/>
                                <TextBlock Grid.Column="1" Name="PatientAgeText" Text="45岁" Foreground="#2C3E50" FontSize="14" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                                    <Rectangle Grid.ColumnSpan="2" Height="1" Fill="#E9ECEF" VerticalAlignment="Bottom" Margin="0,35,0,0"/>
                            </Grid>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="患者编号:" FontWeight="Bold" Foreground="#34495E" FontSize="14" VerticalAlignment="Center"/>
                                <TextBlock Grid.Column="1" Name="PatientIdText" Text="P202401001" Foreground="#2C3E50" FontSize="14" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                                    <Rectangle Grid.ColumnSpan="2" Height="1" Fill="#E9ECEF" VerticalAlignment="Bottom" Margin="0,35,0,0" />
                            </Grid>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="开单科室:" FontWeight="Bold" Foreground="#34495E" FontSize="14" VerticalAlignment="Center"/>
                                <TextBlock Grid.Column="1" Name="OrderDepartmentText" Text="内科" Foreground="#2C3E50" FontSize="14" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                                <Rectangle Grid.ColumnSpan="2" Height="1" Fill="#E9ECEF" VerticalAlignment="Bottom" Margin="0,35,0,0"/>
                            </Grid>

                            <!-- 检查项目 -->
                            <TextBlock Text="检查项目"
                                       FontSize="16"
                                       FontWeight="Bold"
                                       Foreground="#2C3E50"
                                       Margin="0,10,0,10"/>

                            <TextBlock Name="ExamTypeText"
                                       Text="[左尺桡骨,CT平扫+三维重建] [左肩关节,CT平扫+三维重建] [颈椎,CT平扫+三维重建] [胸部、上腹部,CT平扫+三维重建+3D] [颅脑,CT平扫+三维重建]"
                                       Foreground="#2C3E50"
                                       FontSize="14"
                                       LineHeight="24"
                                       TextWrapping="Wrap"
                                       Margin="0,0,0,8"/>
                        </StackPanel>
                    </Border>

                    <!-- 扫码核对区域 -->
                    <Border Grid.Column="2"
                            Background="#F8F9FA"
                            CornerRadius="8"
                            Padding="20"
                            BorderBrush="#E74C3C"
                            BorderThickness="4,0,0,0"
                            Height="400"
                            VerticalAlignment="Top">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" Opacity="0.1" BlurRadius="8"/>
                        </Border.Effect>

                        <StackPanel TextOptions.TextFormattingMode="Display"
                                    TextOptions.TextRenderingMode="ClearType"
                                    UseLayoutRounding="True"
                                    SnapsToDevicePixels="True">
                            <TextBlock Text="扫码核对"
                                       FontSize="18"
                                       FontWeight="Bold"
                                       Foreground="#2C3E50"
                                       Margin="0,0,0,15"/>

                            <!-- 输入区域 -->
                            <StackPanel Margin="0,0,0,20">
                                <TextBlock Text="请扫描患者编号或手动输入:"
                                           FontWeight="Bold"
                                           Foreground="#34495E"
                                           Margin="0,0,0,8"/>

                                <TextBox Name="ScanInputTextBox"
                                         FontSize="16"
                                         Padding="12"
                                         BorderBrush="#BDC3C7"
                                         BorderThickness="2"
                                         Background="White"
                                         Text=""
                                         PreviewKeyDown="ScanInputTextBox_PreviewKeyDown"
                                         TextOptions.TextFormattingMode="Display"
                                         TextOptions.TextRenderingMode="ClearType"
                                         UseLayoutRounding="True"
                                         SnapsToDevicePixels="True">
                                    <TextBox.Style>
                                        <Style TargetType="TextBox">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="TextBox">
                                                        <Border Background="{TemplateBinding Background}"
                                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                                CornerRadius="6">
                                                            <ScrollViewer x:Name="PART_ContentHost"/>
                                                        </Border>
                                                        <ControlTemplate.Triggers>
                                                            <Trigger Property="IsFocused" Value="True">
                                                                <Setter Property="BorderBrush" Value="#3498DB"/>
                                                                <Setter Property="Effect">
                                                                    <Setter.Value>
                                                                        <DropShadowEffect Color="#3498DB" Direction="0" ShadowDepth="0" Opacity="0.3" BlurRadius="8"/>
                                                                    </Setter.Value>
                                                                </Setter>
                                                            </Trigger>
                                                        </ControlTemplate.Triggers>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </TextBox.Style>
                                </TextBox>
                            </StackPanel>

                            <!-- 按钮组 -->
                            <Grid Margin="0,15,0,20">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="10"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <Button Grid.Column="0"
                                        Name="VerifyButton"
                                        Content="核对"
                                        FontSize="14"
                                        FontWeight="Bold"
                                        Height="40"
                                        Background="#3498DB"
                                        Foreground="White"
                                        BorderThickness="0"
                                        Click="VerifyButton_Click"
                                        Style="{DynamicResource VerifyButtonStyle}"/>

                                <Button Grid.Column="2"
                                        Name="ClearButton"
                                        Content="清空"
                                        FontSize="14"
                                        FontWeight="Bold"
                                        Height="40"
                                        Background="#95A5A6"
                                        Foreground="White"
                                        BorderThickness="0"
                                        Click="ClearButton_Click"
                                        Style="{DynamicResource ClearButtonStyle}"/>
                            </Grid>

                            <!-- 核对结果显示区域 -->
                            <Border Name="VerificationResultBorder"
                                    CornerRadius="8"
                                    Padding="15"
                                    Visibility="Collapsed">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Name="ResultIconText"
                                               FontSize="48"
                                               HorizontalAlignment="Center"
                                               Margin="0,0,0,10"/>
                                    <TextBlock Name="ResultMessageText"
                                               FontSize="16"
                                               FontWeight="Bold"
                                               HorizontalAlignment="Center"
                                               TextWrapping="Wrap"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </Border>
                </Grid>

                    <!-- 状态栏 -->
                    <Border Grid.Row="1"
                            Background="#34495E"
                            CornerRadius="6"
                            Padding="20,10"
                            Margin="0,10,0,0">
                    <Grid TextOptions.TextFormattingMode="Display"
                          TextOptions.TextRenderingMode="ClearType"
                          UseLayoutRounding="True"
                          SnapsToDevicePixels="True">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0"
                                   Name="VerificationStatusText"
                                   Text="状态：未核对"
                                   Foreground="White"
                                   FontSize="14"
                                   VerticalAlignment="Center"/>

                        <TextBlock Grid.Column="1"
                                   Name="OperatorInfoText"
                                   Text="当前登录: 加载中..."
                                   Foreground="White"
                                   FontSize="14"
                                   VerticalAlignment="Center"
                                   HorizontalAlignment="Right"/>
                    </Grid>
                    </Border>
                </Grid>
            </Border>
        </Grid>
    </Border>

    <!-- 窗口资源样式 -->
    <Window.Resources>
        <!-- 最小化按钮样式 -->
        <Style x:Key="MinimizeButtonStyle" TargetType="Button">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="ButtonBorder"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="3">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#BDC3C7"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#95A5A6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 关闭按钮样式 -->
        <Style x:Key="CloseButtonStyle" TargetType="Button">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="ButtonBorder"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="3">
                            <ContentPresenter x:Name="ContentPresenter"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#E74C3C"/>
                                <Setter TargetName="ContentPresenter" Property="TextElement.Foreground" Value="White"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#C0392B"/>
                                <Setter TargetName="ContentPresenter" Property="TextElement.Foreground" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 核对按钮样式 -->
        <Style x:Key="VerifyButtonStyle" TargetType="Button">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="ButtonBorder"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              TextOptions.TextFormattingMode="Display"
                                              TextOptions.TextRenderingMode="ClearType"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#2980B9"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#21618C"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 清空按钮样式 -->
        <Style x:Key="ClearButtonStyle" TargetType="Button">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="ButtonBorder"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              TextOptions.TextFormattingMode="Display"
                                              TextOptions.TextRenderingMode="ClearType"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#7F8C8D"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#566573"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
</Window>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;
using System.IO;
using System.Threading.Tasks;
using System.Xml;

namespace CheckPatientInfo
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        private bool _isVerified = false;
        private string _verifierName = "";
        private DateTime _verificationTime;
        private PatientDataService _dataService;
        private PatientCheckInfo _currentPatientInfo;
        private string _currentUserId;
        private string _checkSerialNum;

        public MainWindow()
        {
            InitializeComponent();

            // 解析命令行参数
            ParseCommandLineArguments();

            // 初始化数据服务
            try
            {
                _dataService = new PatientDataService();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化数据服务失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                this.Close();
                return;
            }

            // 加载患者信息
            LoadPatientInfoAsync();

            // 设置焦点到输入框
            this.Loaded += MainWindow_Loaded;

            // 改善文本显示质量（.NET 4.5.2兼容方式）
            this.SetValue(RenderOptions.BitmapScalingModeProperty, BitmapScalingMode.HighQuality);
        }

        /// <summary>
        /// 窗口加载完成事件
        /// </summary>
        private void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            ScanInputTextBox.Focus();
        }

        /// <summary>
        /// 解析命令行参数
        /// </summary>
        private void ParseCommandLineArguments()
        {
            try
            {
                string[] args = Environment.GetCommandLineArgs();

                if (args.Length <= 1)
                {
                    MessageBox.Show("缺少必要的启动参数！\n\n使用方法：CheckPatientInfo.exe 用户ID#检查流水号\n示例：CheckPatientInfo.exe 30B43B10C47246A6B49A6609BE32A12B#20250730002634",
                        "参数错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    Environment.Exit(1);
                    return;
                }

                // 参数格式：用户ID#检查流水号
                string[] parts = args[1].Split('#');
                if (parts.Length != 2 || string.IsNullOrWhiteSpace(parts[0]) || string.IsNullOrWhiteSpace(parts[1]))
                {
                    MessageBox.Show($"参数格式错误！\n\n传入的参数：{args[1]}\n正确格式：用户ID#检查流水号\n示例：30B43B10C47246A6B49A6609BE32A12B#20250730002634",
                        "参数错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    Environment.Exit(1);
                    return;
                }

                _currentUserId = parts[0].Trim();
                _checkSerialNum = parts[1].Trim();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"解析命令行参数失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Environment.Exit(1);
            }
        }

        /// <summary>
        /// 异步加载患者信息
        /// </summary>
        private async void LoadPatientInfoAsync()
        {
            try
            {
                // 显示加载状态
                PatientNameText.Text = "正在加载...";

                // 从数据库获取患者信息
                _currentPatientInfo = await _dataService.GetPatientCheckInfoAsync(_checkSerialNum, _currentUserId);

                if (_currentPatientInfo == null)
                {
                    string errorMsg = $"未找到对应的患者信息！\n\n查询参数：\n检查流水号：{_checkSerialNum}\n用户ID：{_currentUserId}\n\n请检查参数是否正确。";
                    MessageBox.Show(errorMsg, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    this.Close();
                    return;
                }

                // 更新界面显示
                UpdatePatientInfoDisplay();

                // 检查是否已经核对过
                CheckVerificationStatus();
            }
            catch (Exception ex)
            {
                string errorMsg = $"加载患者信息失败：{ex.Message}\n\n查询参数：\n检查流水号：{_checkSerialNum}\n用户ID：{_currentUserId}";
                MessageBox.Show(errorMsg, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                this.Close();
            }
        }

        /// <summary>
        /// 更新患者信息显示
        /// </summary>
        private void UpdatePatientInfoDisplay()
        {
            if (_currentPatientInfo == null) return;

            PatientNameText.Text = _currentPatientInfo.PatientName ?? "";
            PatientGenderText.Text = _currentPatientInfo.Sex ?? "";
            PatientAgeText.Text = _currentPatientInfo.Age ?? "";
            PatientIdText.Text = _currentPatientInfo.PatientId ?? "";
            OrderDepartmentText.Text = _currentPatientInfo.DepartmentName ?? "";
            ExamTypeText.Text = _currentPatientInfo.StudyScription ?? "";

            // 更新标题中的患者类型信息
            if (!string.IsNullOrEmpty(_currentPatientInfo.TypeName))
            {
                this.Title = $"患者入室核对 ({_currentPatientInfo.TypeName})";
            }

            // 更新状态栏的当前登录用户
            OperatorInfoText.Text = $"当前登录: {_currentPatientInfo.CurrentUser ?? "未知用户"}";
        }

        /// <summary>
        /// 检查核对状态
        /// </summary>
        private void CheckVerificationStatus()
        {
            if (_currentPatientInfo != null &&
                !string.IsNullOrEmpty(_currentPatientInfo.CheckVerifyId) &&
                _currentPatientInfo.CheckVerifyTime.HasValue)
            {
                // 已经核对过
                _isVerified = true;
                _verifierName = _currentPatientInfo.CheckVerifyId;
                _verificationTime = _currentPatientInfo.CheckVerifyTime.Value;

                // 禁用输入和按钮
                ScanInputTextBox.IsEnabled = false;
                VerifyButton.IsEnabled = false;
                ClearButton.IsEnabled = false;

                // 显示已核对的结果
                ShowAlreadyVerifiedResult();

                UpdateVerificationStatus();
            }
            else
            {
                InitializeVerificationStatus();
            }
        }

        /// <summary>
        /// 显示已核对的结果
        /// </summary>
        private void ShowAlreadyVerifiedResult()
        {
            // 显示结果区域
            VerificationResultBorder.Visibility = Visibility.Visible;

            // 设置为成功样式
            VerificationResultBorder.Background = new SolidColorBrush(Color.FromRgb(212, 237, 218));
            VerificationResultBorder.BorderBrush = new SolidColorBrush(Color.FromRgb(195, 230, 203));
            VerificationResultBorder.BorderThickness = new Thickness(2);

            ResultIconText.Text = "✔";
            ResultIconText.Foreground = new SolidColorBrush(Color.FromRgb(21, 87, 36));

            ResultMessageText.Text = $"[{_verifierName}]已核对，无需二次核对";
            ResultMessageText.Foreground = new SolidColorBrush(Color.FromRgb(21, 87, 36));
        }

        /// <summary>
        /// 初始化核对状态显示
        /// </summary>
        private void InitializeVerificationStatus()
        {
            UpdateVerificationStatus();
        }

        /// <summary>
        /// 更新核对状态显示
        /// </summary>
        private void UpdateVerificationStatus()
        {
            if (_isVerified)
            {
                VerificationStatusText.Text = $"核对人：{_verifierName}  核对时间：{_verificationTime:yyyy-MM-dd HH:mm:ss}";
            }
            else
            {
                VerificationStatusText.Text = "状态：未核对";
            }
        }

        /// <summary>
        /// 标题栏鼠标拖拽事件
        /// </summary>
        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ButtonState == MouseButtonState.Pressed)
            {
                this.DragMove();
            }
        }

        /// <summary>
        /// 最小化按钮点击事件
        /// </summary>
        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            this.WindowState = WindowState.Minimized;
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }



        /// <summary>
        /// 输入框键盘事件预处理
        /// </summary>
        private void ScanInputTextBox_PreviewKeyDown(object sender, KeyEventArgs e)
        {
            // 只处理Enter键，其他键让系统默认处理
            if (e.Key == Key.Enter)
            {
                VerifyPatient();
                e.Handled = true;
            }
            // 对于所有其他键（包括Ctrl+C, Ctrl+X等），不设置e.Handled，让系统正常处理
        }

        /// <summary>
        /// 核对按钮点击事件
        /// </summary>
        private void VerifyButton_Click(object sender, RoutedEventArgs e)
        {
            VerifyPatient();
        }

        /// <summary>
        /// 清空按钮点击事件
        /// </summary>
        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            ClearInput();
        }

        /// <summary>
        /// 患者核对功能
        /// </summary>
        private async void VerifyPatient()
        {
            if (_currentPatientInfo == null)
            {
                MessageBox.Show("患者信息未加载完成，请稍后再试！", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            string scannedId = ScanInputTextBox.Text.Trim();
            string expectedId = _currentPatientInfo.PatientId;

            if (string.IsNullOrEmpty(scannedId))
            {
                MessageBox.Show("请输入或扫描患者编号！", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                ScanInputTextBox.Focus();
                return;
            }

            try
            {
                bool verificationResult = false;

                // 根据配置值决定核对方式
                if (_currentPatientInfo.ConfigValue == "1")
                {
                    // 配置值为1，需要查询队列信息进行核对
                    verificationResult = await VerifyWithQueueInfoAsync(scannedId);
                }
                else
                {
                    // 配置值不为1，直接比较患者编号
                    verificationResult = (scannedId == expectedId);
                }

                // 显示结果区域
                VerificationResultBorder.Visibility = Visibility.Visible;

                if (verificationResult)
                {
                    // 核对成功，更新数据库
                    bool updateSuccess = await _dataService.UpdateVerificationAsync(_checkSerialNum, _currentUserId);

                    if (updateSuccess)
                    {
                        ShowVerificationSuccess();

                        // 更新核对状态
                        _isVerified = true;
                        _verifierName = _currentPatientInfo.CurrentUser ?? "未知用户";
                        _verificationTime = DateTime.Now;
                        UpdateVerificationStatus();

                        // 禁用输入和按钮
                        ScanInputTextBox.IsEnabled = false;
                        VerifyButton.IsEnabled = false;
                        ClearButton.IsEnabled = false;
                    }
                    else
                    {
                        ShowVerificationError("核对成功但保存失败，请联系管理员！");
                    }
                }
                else
                {
                    // 核对失败
                    ShowVerificationError($"核对失败！患者号{scannedId}与当前患者不匹配！");
                }
            }
            catch (Exception ex)
            {
                ShowVerificationError($"核对过程中发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 使用队列信息进行核对
        /// </summary>
        /// <param name="scannedId">扫描的患者编号</param>
        /// <returns>核对结果</returns>
        private async Task<bool> VerifyWithQueueInfoAsync(string scannedId)
        {
            try
            {
                // 首先检查扫描的编号是否与患者编号一致
                if (scannedId != _currentPatientInfo.PatientId)
                {
                    return false;
                }

                // 查询队列信息
                QueueInfo queueInfo = await _dataService.GetQueueInfoAsync(_currentPatientInfo.StudyId);

                if (queueInfo == null)
                {
                    return false;
                }

                // 比较姓名和性别
                return queueInfo.PatientName == _currentPatientInfo.PatientName &&
                       queueInfo.Sex == _currentPatientInfo.Sex;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 显示核对成功结果
        /// </summary>
        private void ShowVerificationSuccess()
        {
            VerificationResultBorder.Background = new SolidColorBrush(Color.FromRgb(212, 237, 218));
            VerificationResultBorder.BorderBrush = new SolidColorBrush(Color.FromRgb(195, 230, 203));
            VerificationResultBorder.BorderThickness = new Thickness(2);

            ResultIconText.Text = "✔";
            ResultIconText.Foreground = new SolidColorBrush(Color.FromRgb(21, 87, 36));

            ResultMessageText.Text = "核对通过！患者信息匹配成功";
            ResultMessageText.Foreground = new SolidColorBrush(Color.FromRgb(21, 87, 36));
        }

        /// <summary>
        /// 显示核对失败结果
        /// </summary>
        /// <param name="message">错误消息</param>
        private void ShowVerificationError(string message)
        {
            VerificationResultBorder.Background = new SolidColorBrush(Color.FromRgb(248, 215, 218));
            VerificationResultBorder.BorderBrush = new SolidColorBrush(Color.FromRgb(245, 198, 203));
            VerificationResultBorder.BorderThickness = new Thickness(2);

            ResultIconText.Text = "❌";
            ResultIconText.Foreground = new SolidColorBrush(Color.FromRgb(114, 28, 36));

            ResultMessageText.Text = message;
            ResultMessageText.Foreground = new SolidColorBrush(Color.FromRgb(114, 28, 36));
        }

        /// <summary>
        /// 清空输入
        /// </summary>
        private void ClearInput()
        {
            // 如果已经核对过，不允许清空
            if (_isVerified)
            {
                return;
            }

            ScanInputTextBox.Text = "";
            VerificationResultBorder.Visibility = Visibility.Collapsed;

            ScanInputTextBox.Focus();
        }

        /// <summary>
        /// 窗口关闭时清理资源
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            try
            {
                _dataService?.Dispose();
            }
            catch
            {
                // 忽略清理时的异常
            }

            base.OnClosed(e);
        }
    }
}

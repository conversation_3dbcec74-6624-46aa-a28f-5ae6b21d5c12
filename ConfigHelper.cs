using System;
using System.IO;

namespace CheckPatientInfo
{
    /// <summary>
    /// 配置文件帮助类
    /// </summary>
    public static class ConfigHelper
    {
        /// <summary>
        /// 获取数据库连接URL
        /// </summary>
        /// <returns>数据库连接URL</returns>
        public static string GetConnectionUrl()
        {
            try
            {
                string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Connection.ini");
                
                if (!File.Exists(configPath))
                {
                    throw new FileNotFoundException($"配置文件不存在：{configPath}");
                }

                string[] lines = File.ReadAllLines(configPath);
                bool inConnectionSection = false;

                foreach (string line in lines)
                {
                    string trimmedLine = line.Trim();
                    
                    // 检查是否进入[Connection]节
                    if (trimmedLine.Equals("[Connection]", StringComparison.OrdinalIgnoreCase))
                    {
                        inConnectionSection = true;
                        continue;
                    }
                    
                    // 如果遇到其他节，退出Connection节
                    if (trimmedLine.StartsWith("[") && trimmedLine.EndsWith("]") && 
                        !trimmedLine.Equals("[Connection]", StringComparison.OrdinalIgnoreCase))
                    {
                        inConnectionSection = false;
                        continue;
                    }
                    
                    // 在Connection节中查找Url配置
                    if (inConnectionSection && trimmedLine.StartsWith("Url=", StringComparison.OrdinalIgnoreCase))
                    {
                        return trimmedLine.Substring(4).Trim();
                    }
                }

                throw new InvalidOperationException("在Connection.ini文件中未找到[Connection]节下的Url配置");
            }
            catch (Exception ex)
            {
                throw new Exception($"读取配置文件失败：{ex.Message}");
            }
        }
    }
}

# 程序启动问题调试说明

## 问题描述
程序编译成功，但运行时没有任何界面、提示或弹窗出现。

## 已添加的调试功能

### 1. 全局异常处理
在 `App.xaml.cs` 中添加了全局异常捕获：
- `OnStartup` 方法捕获启动异常
- `DispatcherUnhandledException` 捕获UI线程异常
- `UnhandledException` 捕获所有未处理异常

### 2. 构造函数异常处理
在 `MainWindow` 构造函数中添加了 try-catch 块，捕获初始化过程中的异常。

### 3. 修改退出方式
- 将 `Environment.Exit(1)` 改为 `Application.Current.Shutdown(1)`
- 将 `this.Close()` 改为 `Application.Current.Shutdown(1)`

## 调试步骤

### 步骤1：重新编译
```cmd
msbuild CheckPatientInfo.sln /p:Configuration=Debug /p:Platform="Any CPU"
```

### 步骤2：测试启动
```cmd
# 测试1：不带参数启动（应该显示参数错误提示）
CheckPatientInfo.exe

# 测试2：带正确参数启动
CheckPatientInfo.exe super#20250729004270
```

### 步骤3：检查可能的问题

#### A. 依赖项问题
确认以下文件存在：
- `Connection.ini` 文件
- 所有必要的 .NET Framework 4.5.2 组件

#### B. 权限问题
- 确认程序有读取配置文件的权限
- 确认程序有网络访问权限（如果需要连接数据库）

#### C. 配置文件问题
检查 `Connection.ini` 文件格式：
```ini
[Connection]
Url=http://10.0.252.111:8090/PacsDBProxy/PacsDatabase/PacsDatabase.action
```

### 步骤4：查看错误信息
现在程序应该会显示具体的错误信息，包括：
- 参数错误
- 配置文件读取错误
- 数据库连接错误
- 其他初始化错误

## 可能的问题原因

### 1. 配置文件问题
- `Connection.ini` 文件不存在
- 配置文件格式错误
- 配置文件编码问题

### 2. 网络连接问题
- 无法访问数据库服务器
- 防火墙阻止连接
- 代理设置问题

### 3. 依赖项问题
- 缺少 .NET Framework 4.5.2
- 缺少必要的 Windows 组件
- 缺少 Visual C++ 运行库

### 4. 权限问题
- 程序没有读取文件权限
- 程序没有网络访问权限
- 用户账户控制(UAC)阻止

## 下一步调试

如果程序仍然没有任何反应，请尝试：

1. **命令行运行**：在命令提示符中运行，查看是否有控制台输出
2. **事件查看器**：检查Windows事件日志中的应用程序错误
3. **依赖项检查**：使用Dependency Walker检查缺少的DLL
4. **简化测试**：创建最简单的WPF程序测试环境

## 临时解决方案

如果问题持续，可以：
1. 创建一个最简化的版本，逐步添加功能
2. 添加更多的日志输出到文件
3. 使用调试器逐步执行

现在请重新编译并运行程序，应该能看到具体的错误信息了。

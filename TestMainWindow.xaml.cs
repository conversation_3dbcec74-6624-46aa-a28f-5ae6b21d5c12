using System;
using System.Windows;

namespace CheckPatientInfo
{
    /// <summary>
    /// 测试版本的MainWindow，用于调试启动问题
    /// </summary>
    public partial class TestMainWindow : Window
    {
        public TestMainWindow()
        {
            try
            {
                InitializeComponent();
                MessageBox.Show("程序启动成功！", "测试", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"InitializeComponent失败：{ex.Message}\n\n详细信息：{ex.ToString()}", 
                    "初始化错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}

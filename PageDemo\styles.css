/* 影像系统入室核对模块样式 - 适用于WPF/WinForm复刻 */

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 主体样式 */
body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Aria<PERSON>, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
}

/* 主容器 */
.container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    padding: 30px;
    width: 900px;
    max-width: 95vw;
    min-height: 600px;
}

/* 标题区域 */
.header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.header h1 {
    color: #2c3e50;
    font-size: 28px;
    margin-bottom: 10px;
    font-weight: 600;
}

.header .subtitle {
    color: #7f8c8d;
    font-size: 16px;
    font-style: italic;
}

/* 主要内容区域 */
.main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

/* 患者信息面板 */
.patient-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    border-left: 4px solid #3498db;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.patient-info h3 {
    color: #2c3e50;
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 600;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding: 6px 0;
    border-bottom: 1px solid #e9ecef;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: #34495e;
    min-width: 70px;
    font-size: 14px;
}

.info-value {
    color: #2c3e50;
    flex: 1;
    text-align: right;
    font-weight: 500;
    font-size: 14px;
}

.exam-content {
    color: #2c3e50;
    font-size: 14px;
    line-height: 1.5;
    word-wrap: break-word;
    margin-top: 8px;
}

/* 扫码区域 */
.scan-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid #e74c3c;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.scan-section h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: 600;
}

.scan-input-group {
    margin-bottom: 20px;
}

.scan-input-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #34495e;
}

.scan-input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #bdc3c7;
    border-radius: 6px;
    font-size: 16px;
    font-family: 'Consolas', 'Courier New', monospace;
    transition: all 0.3s ease;
    background: white;
}

.scan-input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    background: #fafbfc;
}

/* 按钮组 */
.btn-group {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #7f8c8d, #6c7b7d);
}

/* 核对结果区域 */
.verification-result-inline {
    margin-top: 20px;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    font-size: 16px;
    font-weight: 600;
    display: none;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.result-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
    border: 2px solid #28a745;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
}

.result-error {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
    border: 2px solid #dc3545;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.2);
}

.result-icon {
    font-size: 48px;
    margin-bottom: 10px;
    animation: pulse 1s ease-in-out;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* 状态栏 */
.status-bar {
    background: linear-gradient(135deg, #34495e, #2c3e50);
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-time {
    font-family: 'Consolas', 'Courier New', monospace;
    font-weight: 500;
}

.status-user {
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .container {
        padding: 20px;
        margin: 10px;
    }
    
    .header h1 {
        font-size: 24px;
    }
    
    .status-bar {
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .container {
        border: 2px solid #000;
    }
    
    .btn {
        border: 2px solid #000;
    }
    
    .scan-input {
        border: 2px solid #000;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    body {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }
    
    .container {
        background: #3a3a3a;
        color: #f0f0f0;
    }
    
    .patient-info, .scan-section {
        background: #4a4a4a;
    }
    
    .scan-input {
        background: #5a5a5a;
        color: #f0f0f0;
        border-color: #6a6a6a;
    }
}

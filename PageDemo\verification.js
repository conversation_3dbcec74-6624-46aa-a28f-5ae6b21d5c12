// 影像系统入室核对模块 JavaScript 逻辑
// 适用于WPF/WinForm复刻参考

class PatientVerificationSystem {
    constructor() {
        this.currentPatient = {
            name: '张三',
            gender: '男',
            age: '45岁',
            id: 'P202401001',
            orderDepartment: '内科',
            examType: '[左尺桡骨,CT平扫+三维重建] [左肩关节,CT平扫+三维重建] [颈椎,CT平扫+三维重建] [胸部、上腹部,CT平扫+三维重建+3D] [颅脑,CT平扫+三维重建]'
        };
        
        this.verificationHistory = [];
        this.init();
    }

    init() {
        this.updateTime();
        this.setupEventListeners();
        this.loadPatientInfo();
        
        // 每秒更新时间
        setInterval(() => this.updateTime(), 1000);
        
        // 自动聚焦到输入框
        this.focusInput();
    }

    setupEventListeners() {
        const scanInput = document.getElementById('scanInput');
        const verifyBtn = document.querySelector('.btn-primary');
        const clearBtn = document.querySelector('.btn-secondary');

        // 回车键触发核对
        scanInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.verifyPatient();
            }
        });

        // 输入事件监听（模拟扫码枪）
        scanInput.addEventListener('input', (e) => {
            this.handleScanInput(e.target.value);
        });

        // 按钮事件
        verifyBtn.addEventListener('click', () => this.verifyPatient());
        clearBtn.addEventListener('click', () => this.clearInput());

        // 失焦时重新聚焦（确保扫码枪能正常工作）
        scanInput.addEventListener('blur', () => {
            setTimeout(() => this.focusInput(), 100);
        });
    }

    loadPatientInfo() {
        document.getElementById('patientName').textContent = this.currentPatient.name;
        document.getElementById('patientGender').textContent = this.currentPatient.gender;
        document.getElementById('patientAge').textContent = this.currentPatient.age;
        document.getElementById('patientId').textContent = this.currentPatient.id;
        document.getElementById('orderDepartment').textContent = this.currentPatient.orderDepartment;
        document.getElementById('examType').textContent = this.currentPatient.examType;
    }

    updateTime() {
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        document.getElementById('currentTime').textContent = timeString;
    }

    handleScanInput(value) {
        // 清理输入值（移除可能的特殊字符）
        const cleanValue = value.trim().replace(/[^\w\d]/g, '');
        
        // 如果输入长度达到预期长度，自动触发核对
        if (cleanValue.length >= 9) {
            setTimeout(() => {
                this.verifyPatient();
            }, 200); // 短暂延迟确保输入完成
        }
    }

    verifyPatient() {
        const scanInput = document.getElementById('scanInput');
        const scannedId = scanInput.value.trim();
        const expectedId = this.currentPatient.id;

        if (!scannedId) {
            this.showAlert('请输入或扫描患者编号！', 'warning');
            this.focusInput();
            return;
        }

        const verificationResult = {
            scannedId: scannedId,
            expectedId: expectedId,
            timestamp: new Date(),
            success: scannedId === expectedId
        };

        // 记录核对历史
        this.verificationHistory.push(verificationResult);

        // 显示结果
        this.showVerificationResult(verificationResult);

        // 记录日志（在实际应用中可以发送到服务器）
        this.logVerification(verificationResult);
    }

    showVerificationResult(result) {
        const resultDiv = document.getElementById('verificationResult');
        const resultIcon = document.getElementById('resultIcon');
        const resultMessage = document.getElementById('resultMessage');

        resultDiv.style.display = 'block';

        if (result.success) {
            // 核对成功
            resultDiv.className = 'verification-result-inline result-success';
            resultIcon.textContent = '✅';
            resultMessage.innerHTML = `
                <div>核对通过！</div>
                <div style="font-size: 14px; margin-top: 5px; opacity: 0.8;">
                    患者信息匹配成功，可以进入检查室
                </div>
            `;

            // 播放成功音效（在实际应用中）
            this.playSuccessSound();

            // 3秒后自动清空，准备下一个患者
            setTimeout(() => {
                this.clearInput();
            }, 3000);

        } else {
            // 核对失败
            resultDiv.className = 'verification-result-inline result-error';
            resultIcon.textContent = '❌';
            resultMessage.innerHTML = `
                <div>核对失败！</div>
                <div style="font-size: 14px; margin-top: 5px; opacity: 0.8;">
                    扫描编号: <strong>${result.scannedId}</strong><br>
                    预期编号: <strong>${result.expectedId}</strong><br>
                    请检查患者身份或重新扫描
                </div>
            `;

            // 播放错误音效（在实际应用中）
            this.playErrorSound();
        }

        // 滚动到结果区域
        resultDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }

    clearInput() {
        const scanInput = document.getElementById('scanInput');
        const resultDiv = document.getElementById('verificationResult');
        
        scanInput.value = '';
        resultDiv.style.display = 'none';
        this.focusInput();
    }

    focusInput() {
        const scanInput = document.getElementById('scanInput');
        scanInput.focus();
    }

    showAlert(message, type = 'info') {
        // 简单的提示框实现
        alert(message);
    }

    playSuccessSound() {
        // 在实际应用中可以播放音效
        console.log('播放成功音效');
    }

    playErrorSound() {
        // 在实际应用中可以播放音效
        console.log('播放错误音效');
    }

    logVerification(result) {
        console.log('核对记录:', {
            timestamp: result.timestamp.toISOString(),
            patientId: this.currentPatient.id,
            patientName: this.currentPatient.name,
            scannedId: result.scannedId,
            success: result.success,
            operator: '李医生'
        });
    }

    // 获取核对历史记录
    getVerificationHistory() {
        return this.verificationHistory;
    }

    // 更新患者信息（用于切换患者）
    updatePatientInfo(patientData) {
        this.currentPatient = { ...this.currentPatient, ...patientData };
        this.loadPatientInfo();
        this.clearInput();
    }

    // 导出核对记录（用于报表）
    exportVerificationLog() {
        const logData = this.verificationHistory.map(record => ({
            时间: record.timestamp.toLocaleString('zh-CN'),
            患者编号: record.expectedId,
            扫描编号: record.scannedId,
            核对结果: record.success ? '通过' : '失败'
        }));
        
        console.log('核对记录导出:', logData);
        return logData;
    }

    // 重置系统状态
    reset() {
        this.clearInput();
        this.verificationHistory = [];
        console.log('系统已重置');
    }
}

// 模拟患者数据库
const patientDatabase = {
    'P202401001': {
        name: '张三',
        gender: '男',
        age: '45岁',
        id: 'P202401001',
        orderDepartment: '内科',
        examType: '[左尺桡骨,CT平扫+三维重建] [左肩关节,CT平扫+三维重建] [颈椎,CT平扫+三维重建] [胸部、上腹部,CT平扫+三维重建+3D] [颅脑,CT平扫+三维重建]'
    },
    'P202401002': {
        name: '李四',
        gender: '女',
        age: '32岁',
        id: 'P202401002',
        orderDepartment: '外科',
        examType: '[腹部,MRI平扫+增强] [盆腔,MRI平扫+增强]'
    },
    'P202401003': {
        name: '王五',
        gender: '男',
        age: '58岁',
        id: 'P202401003',
        orderDepartment: '神经科',
        examType: '[颅脑,CT平扫+三维重建]'
    }
};

// 全局变量和函数（保持与HTML的兼容性）
let verificationSystem;

// 页面加载完成后初始化系统
document.addEventListener('DOMContentLoaded', function() {
    verificationSystem = new PatientVerificationSystem();
});

// 全局函数（用于HTML按钮调用）
function verifyPatient() {
    if (verificationSystem) {
        verificationSystem.verifyPatient();
    }
}

function clearInput() {
    if (verificationSystem) {
        verificationSystem.clearInput();
    }
}

// 切换患者功能（用于测试）
function switchPatient(patientId) {
    if (verificationSystem && patientDatabase[patientId]) {
        verificationSystem.updatePatientInfo(patientDatabase[patientId]);
    }
}

// 导出功能（用于WPF/WinForm集成）
window.PatientVerificationAPI = {
    verify: (scannedId) => verificationSystem?.verifyPatient(),
    clear: () => verificationSystem?.clearInput(),
    updatePatient: (patientData) => verificationSystem?.updatePatientInfo(patientData),
    getHistory: () => verificationSystem?.getVerificationHistory(),
    exportLog: () => verificationSystem?.exportVerificationLog(),
    reset: () => verificationSystem?.reset()
};

# 患者入室核对系统调试说明

## 问题描述
程序提示"未找到对应的患者信息！"，但在PL/SQL中执行相同的SQL可以正常查询到数据。

## 问题解决
已发现问题：接口返回的XML格式是Microsoft Rowset格式，与之前的解析方式不匹配。

### 接口返回格式
```xml
<?xml version='1.0' encoding='GBK'?>
<xml xmlns:s='uuid:BDC6E3F0-6DA3-11d1-A2A3-00AA00C14882' xmlns:dt='uuid:C2F41010-65B3-11d1-A29F-00AA00C14882' xmlns:rs='urn:schemas-microsoft-com:rowset' xmlns:z='#RowsetSchema'>
    <s:Schema id="RowsetSchema">
        <s:ElementType name="row" content="eltOnly" rs:CommandTimeout="30" rs:updatable="false">
            <s:AttributeType name="PATIENTNAME" rs:number="1">
                <s:datatype dt:type="string"/>
            </s:AttributeType>
        </s:ElementType>
    </s:Schema>
    <rs:data>
        <z:row PATIENTNAME="张三" SEX="男" AGE="45岁" PATIENTID="P001"/>
    </rs:data>
</xml>
```

### 修复内容
- 修改XML解析逻辑，使用正确的命名空间和节点路径
- 字段数据存储在`<z:row>`节点的属性中，而不是子元素
- 属性名通常是大写格式

## 调试步骤

### 1. 检查命令行参数
现在程序会严格检查命令行参数：
- 如果没有参数或参数格式错误，会显示错误信息并退出
- 不再使用测试数据

### 2. 查看调试输出
程序现在会输出详细的调试信息到VS的输出窗口：

1. **启动VS2022**
2. **打开"输出"窗口**：视图 -> 输出
3. **选择"调试"**作为输出源
4. **运行程序**，查看调试信息

### 3. 调试信息包含
- 解析到的命令行参数（用户ID和检查流水号）
- 数据库连接URL
- 发送的XML请求
- HTTP响应状态和内容
- SQL查询语句
- 数据库返回的原始XML
- XML解析过程

### 4. 可能的问题原因

#### A. 参数传递问题
检查调试输出中的：
```
解析到的用户ID：xxx
解析到的检查流水号：xxx
```
确认参数是否正确传递。

#### B. 数据库连接问题
检查调试输出中的：
```
数据库连接URL：xxx
HTTP响应状态：xxx
```
确认连接是否正常。

#### C. SQL执行问题
检查调试输出中的：
```
执行完整SQL查询：xxx
数据库响应：xxx
```
对比程序生成的SQL与你在PL/SQL中执行的SQL是否一致。

#### D. XML解析问题
检查调试输出中的：
```
开始解析XML响应，长度：xxx
找到数据行，子节点数量：xxx
```
确认XML格式是否正确。

### 5. 测试建议

#### 测试1：简单查询
程序现在会先执行一个简单的count查询：
```sql
select count(*) as cnt from studyinfo where checkserialnum='xxx'
```
查看这个查询是否返回大于0的结果。

#### 测试2：对比SQL
将调试输出中的完整SQL复制到PL/SQL中执行，看是否有结果。

#### 测试3：检查XML响应
查看数据库返回的原始XML格式，确认是否包含预期的数据。

### 6. 常见问题

1. **参数中包含特殊字符**：检查用户ID和检查流水号是否包含单引号等特殊字符
2. **编码问题**：检查数据库返回的中文是否正确显示
3. **权限问题**：确认用户ID对应的用户是否有查询权限
4. **数据不存在**：确认检查流水号在数据库中确实存在

### 7. 如何运行调试版本

```cmd
# 在命令行中运行（替换为实际参数）
CheckPatientInfo.exe 30B43B10C47246A6B49A6609BE32A12B#20250730002634

# 或者在VS中设置调试参数：
# 项目属性 -> 调试 -> 命令行参数
```

### 8. 下一步
请运行程序并将VS输出窗口中的调试信息发给我，我可以帮你分析具体问题。

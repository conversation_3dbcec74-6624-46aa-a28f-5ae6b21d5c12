<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>影像系统 - 入室核对</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 30px;
            width: 800px;
            max-width: 90vw;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 28px;
        }

        .header .subtitle {
            color: #7f8c8d;
            font-size: 16px;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .patient-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            border-left: 4px solid #3498db;
        }

        .patient-info h3 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 16px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 6px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .info-label {
            font-weight: bold;
            color: #34495e;
            min-width: 70px;
            font-size: 14px;
        }

        .info-value {
            color: #2c3e50;
            flex: 1;
            text-align: right;
            font-size: 14px;
        }

        .exam-content {
            color: #2c3e50;
            font-size: 14px;
            line-height: 1.5;
            word-wrap: break-word;
            margin-top: 8px;
        }

        .scan-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #e74c3c;
        }

        .scan-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .scan-input-group {
            margin-bottom: 20px;
        }

        .scan-input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #34495e;
        }

        .scan-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #bdc3c7;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .scan-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            flex: 1;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .verification-result-inline {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            display: none;
        }

        .result-success {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }

        .result-error {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }

        .result-icon {
            font-size: 48px;
            margin-bottom: 10px;
        }

        .status-bar {
            background: #34495e;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            margin-top: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-time {
            font-size: 14px;
        }

        .status-user {
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>影像系统入室核对</h1>
        </div>

        <div class="main-content">
            <!-- 患者信息显示区域 -->
            <div class="patient-info">
                <h3>患者信息（门诊CT）</h3>
                <div class="info-item">
                    <span class="info-label">姓名:</span>
                    <span class="info-value" id="patientName">张三</span>
                </div>
                <div class="info-item">
                    <span class="info-label">性别:</span>
                    <span class="info-value" id="patientGender">男</span>
                </div>
                <div class="info-item">
                    <span class="info-label">年龄:</span>
                    <span class="info-value" id="patientAge">45岁</span>
                </div>
                <div class="info-item">
                    <span class="info-label">患者编号:</span>
                    <span class="info-value" id="patientId">P202401001</span>
                </div>
                <div class="info-item">
                    <span class="info-label">开单科室:</span>
                    <span class="info-value" id="orderDepartment">内科</span>
                </div>

                <h3 style="margin-top: 1px;">检查项目</h3>
                <div class="exam-content" id="examType">
                    [左尺桡骨,CT平扫+三维重建] [左肩关节,CT平扫+三维重建] [颈椎,CT平扫+三维重建] [胸部、上腹部,CT平扫+三维重建+3D] [颅脑,CT平扫+三维重建]
                </div>
            </div>

            <!-- 扫码核对区域 -->
            <div class="scan-section">
                <h3>扫码核对</h3>
                <div class="scan-input-group">
                    <label for="scanInput">请扫描患者编号或手动输入:</label>
                    <input type="text" id="scanInput" class="scan-input" placeholder="请使用扫码枪扫描或手动输入患者编号" autofocus>
                </div>
                <div class="btn-group">
                    <button class="btn btn-primary" onclick="verifyPatient()">核对</button>
                    <button class="btn btn-secondary" onclick="clearInput()">清空</button>
                </div>

                <!-- 核对结果显示区域 -->
                <div id="verificationResult" class="verification-result-inline">
                    <div class="result-icon" id="resultIcon"></div>
                    <div id="resultMessage"></div>
                </div>
            </div>
        </div>

        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-time" id="currentTime"></div>
            <div class="status-user">操作员: 李医生 | 科室: 影像科</div>
        </div>
    </div>

    <script>
        // 初始化患者信息
        function initPatientInfo() {
            document.getElementById('patientName').textContent = '张三';
            document.getElementById('patientGender').textContent = '男';
            document.getElementById('patientAge').textContent = '45岁';
            document.getElementById('patientId').textContent = 'P202401001';
            document.getElementById('orderDepartment').textContent = '内科';
            document.getElementById('examType').textContent = '[左尺桡骨,CT平扫+三维重建] [左肩关节,CT平扫+三维重建] [颈椎,CT平扫+三维重建] [胸部、上腹部,CT平扫+三维重建+3D] [颅脑,CT平扫+三维重建]';
        }

        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN');
            document.getElementById('currentTime').textContent = timeString;
        }

        // 患者核对功能
        function verifyPatient() {
            const scanInput = document.getElementById('scanInput');
            const scannedId = scanInput.value.trim();
            const expectedId = document.getElementById('patientId').textContent;
            const resultDiv = document.getElementById('verificationResult');
            const resultIcon = document.getElementById('resultIcon');
            const resultMessage = document.getElementById('resultMessage');

            if (!scannedId) {
                alert('请输入或扫描患者编号！');
                return;
            }

            resultDiv.style.display = 'block';

            if (scannedId === expectedId) {
                // 核对成功
                resultDiv.className = 'verification-result-inline result-success';
                resultIcon.textContent = '✅';
                resultMessage.textContent = '核对通过！患者信息匹配成功';
            } else {
                // 核对失败
                resultDiv.className = 'verification-result-inline result-error';
                resultIcon.textContent = '❌';
                resultMessage.textContent = `核对失败！扫描编号: ${scannedId}，预期编号: ${expectedId}`;
            }
        }

        // 清空输入
        function clearInput() {
            document.getElementById('scanInput').value = '';
            document.getElementById('verificationResult').style.display = 'none';
            document.getElementById('scanInput').focus();
        }

        // 回车键触发核对
        document.getElementById('scanInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                verifyPatient();
            }
        });

        // 初始化
        initPatientInfo();
        updateTime();
        setInterval(updateTime, 1000);

        // 模拟扫码枪输入（通常扫码枪会在输入后自动按回车）
        document.getElementById('scanInput').addEventListener('input', function(e) {
            // 如果输入长度达到预期长度，可以自动触发核对
            if (e.target.value.length >= 9) { // 假设患者编号长度为9位
                setTimeout(() => {
                    verifyPatient();
                }, 100);
            }
        });
    </script>
</body>
</html>

using System;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml;
using vocabulary.Utils;

namespace vocabulary.Services
{
    public class DatabaseService : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly string _connectionUrl;

        public DatabaseService()
        {
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(30); // 设置30秒超时
            _connectionUrl = ConfigHelper.GetConnectionUrl();
        }

        public async Task<string> ExecuteQueryAsync(string sql, int offset = 0, int limit = 10)
        {
            try
            {
                string xmlRequest = CreateQueryXml(sql, offset, limit);
                System.Diagnostics.Debug.WriteLine($"发送的XML请求：{xmlRequest}");
                
                var content = new StringContent(xmlRequest, Encoding.UTF8, "application/xml");
                
                HttpResponseMessage response = await _httpClient.PostAsync(_connectionUrl, content);
                
                System.Diagnostics.Debug.WriteLine($"HTTP响应状态：{response.StatusCode}");
                System.Diagnostics.Debug.WriteLine($"HTTP响应头：{response.Headers}");
                
                // 获取响应字节数组
                byte[] responseBytes = await response.Content.ReadAsByteArrayAsync();
                string responseContent = ConvertGbkToUtf8(responseBytes);
                
                System.Diagnostics.Debug.WriteLine($"HTTP响应内容：{responseContent}");
                
                // 检查是否返回了错误页面而不是XML数据
                if (IsErrorResponse(responseContent))
                {
                    throw new Exception($"服务器返回错误：{ExtractErrorMessage(responseContent)}");
                }
                
                return responseContent;
            }
            catch (Exception ex)
            {
                throw new Exception($"数据库查询失败: {ex.Message}");
            }
        }

        public async Task<string> ExecuteUpdateAsync(string sql)
        {
            try
            {
                string xmlRequest = CreateUpdateXml(sql);
                var content = new StringContent(xmlRequest, Encoding.UTF8, "application/xml");
                
                HttpResponseMessage response = await _httpClient.PostAsync(_connectionUrl, content);
                
                // 获取响应字节数组
                byte[] responseBytes = await response.Content.ReadAsByteArrayAsync();
                
                // 无损转换GBK到UTF-8
                return ConvertGbkToUtf8(responseBytes);
            }
            catch (Exception ex)
            {
                throw new Exception($"数据库更新失败: {ex.Message}");
            }
        }

        private string CreateQueryXml(string sql, int offset, int limit)
        {
            return $@"<request>
    <sqltype>query</sqltype>
    <offset>{offset}</offset>
    <limit>{limit}</limit>
    <statements>
        <statement name=""query1"">
            <sql><![CDATA[{sql}]]></sql>
            <input>
            </input>
            <out>
            </out>
        </statement>
    </statements>
</request>";
        }

        private string CreateUpdateXml(string sql)
        {
            return $@"<request>
    <sqltype>update</sqltype>
    <offset>0</offset>
    <limit>10</limit>
    <statements>
        <statement name=""update1"">
            <sql><![CDATA[{sql}]]></sql>
            <input>
            </input>
            <out>
            </out>
        </statement>
    </statements>
</request>";
        }

        /// <summary>
        /// 无损转换GBK字节数组到UTF-8字符串
        /// </summary>
        /// <param name="gbkBytes">GBK编码的字节数组</param>
        /// <returns>UTF-8字符串</returns>
        private string ConvertGbkToUtf8(byte[] gbkBytes)
        {
            try
            {
                // 首先尝试检测编码
                string result = DetectAndConvertEncoding(gbkBytes);
                if (result != null)
                {
                    // 清理XML内容，去掉可能导致解析失败的问题
                    return CleanXmlContent(result);
                }

                // 如果检测失败，使用GBK编码解析
                Encoding gbkEncoding = Encoding.GetEncoding("GBK");
                string gbkString = gbkEncoding.GetString(gbkBytes);
                
                // 转换为UTF-8字节数组，再转换为字符串（这一步实际上是多余的，但保持一致性）
                byte[] utf8Bytes = Encoding.UTF8.GetBytes(gbkString);
                string utf8String = Encoding.UTF8.GetString(utf8Bytes);
                
                // 清理XML内容
                return CleanXmlContent(utf8String);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"GBK转UTF-8失败：{ex.Message}");
                // 如果转换失败，尝试直接用UTF-8解析
                try
                {
                    string utf8Result = Encoding.UTF8.GetString(gbkBytes);
                    return CleanXmlContent(utf8Result);
                }
                catch
                {
                    // 最后的兜底方案，使用默认编码
                    string defaultResult = Encoding.Default.GetString(gbkBytes);
                    return CleanXmlContent(defaultResult);
                }
            }
        }

        /// <summary>
        /// 检查响应是否为错误页面
        /// </summary>
        /// <param name="responseContent">响应内容</param>
        /// <returns>是否为错误响应</returns>
        private bool IsErrorResponse(string responseContent)
        {
            if (string.IsNullOrEmpty(responseContent))
                return false;

            string content = responseContent.ToLower();
            
            // 检查是否包含HTML错误页面的特征
            return content.Contains("<html") || 
                   content.Contains("http status") || 
                   content.Contains("error") && content.Contains("<title>") ||
                   content.Contains("exception") && content.Contains("<body>") ||
                   content.Contains("500") && content.Contains("<h1>") ||
                   content.Contains("内部服务器错误") ||
                   content.Contains("internal server error");
        }

        /// <summary>
        /// 从错误响应中提取错误信息
        /// </summary>
        /// <param name="errorResponse">错误响应内容</param>
        /// <returns>提取的错误信息</returns>
        private string ExtractErrorMessage(string errorResponse)
        {
            try
            {
                if (string.IsNullOrEmpty(errorResponse))
                    return "未知错误";

                // 尝试提取HTML title中的错误信息
                var titleMatch = Regex.Match(errorResponse, @"<title[^>]*>([^<]+)</title>", RegexOptions.IgnoreCase);
                if (titleMatch.Success)
                {
                    return titleMatch.Groups[1].Value.Trim();
                }

                // 尝试提取h1标签中的错误信息
                var h1Match = Regex.Match(errorResponse, @"<h1[^>]*>([^<]+)</h1>", RegexOptions.IgnoreCase);
                if (h1Match.Success)
                {
                    return h1Match.Groups[1].Value.Trim();
                }

                // 如果是HTML页面，返回简化的错误信息
                if (errorResponse.ToLower().Contains("<html"))
                {
                    if (errorResponse.Contains("500"))
                        return "HTTP 500 - 内部服务器错误";
                    if (errorResponse.Contains("404"))
                        return "HTTP 404 - 页面未找到";
                    if (errorResponse.Contains("403"))
                        return "HTTP 403 - 访问被拒绝";
                    
                    return "服务器返回HTML错误页面";
                }

                // 返回前200个字符作为错误信息
                return errorResponse.Length > 200 ? errorResponse.Substring(0, 200) + "..." : errorResponse;
            }
            catch
            {
                return "解析错误信息失败";
            }
        }

        /// <summary>
        /// 清理XML内容，修复常见的XML格式问题
        /// </summary>
        /// <param name="xmlContent">原始XML内容</param>
        /// <returns>清理后的XML内容</returns>
        private string CleanXmlContent(string xmlContent)
        {
            if (string.IsNullOrEmpty(xmlContent))
                return xmlContent;

            try
            {
                System.Diagnostics.Debug.WriteLine($"清理前XML内容：{xmlContent}");
                
                // 1. 修复DOCTYPE声明问题
                // 将错误的 <!doctype 替换为正确的 <!DOCTYPE
                xmlContent = Regex.Replace(xmlContent, @"<!\s*doctype\s+", "<!DOCTYPE ", RegexOptions.IgnoreCase);
                
                // 2. 移除可能导致问题的DOCTYPE声明（如果无法修复）
                xmlContent = Regex.Replace(xmlContent, @"<!\s*DOCTYPE[^>]*>", "", RegexOptions.IgnoreCase);
                
                // 3. 移除XML声明中的问题字符
                xmlContent = Regex.Replace(xmlContent, @"<\?xml[^>]*\?>", "<?xml version='1.0' encoding='UTF-8'?>");
                
                // 4. 移除HTML实体引用，替换为XML实体
                xmlContent = xmlContent.Replace("&nbsp;", " ");
                xmlContent = xmlContent.Replace("&copy;", "©");
                xmlContent = xmlContent.Replace("&reg;", "®");
                
                // 5. 修复未闭合的标签（简单处理）
                xmlContent = Regex.Replace(xmlContent, @"<([^/>]+)>([^<]*)</\1>", "<$1>$2</$1>");
                
                // 6. 确保XML格式正确
                if (!xmlContent.TrimStart().StartsWith("<?xml") && !xmlContent.TrimStart().StartsWith("<"))
                {
                    // 如果不是以XML标签开始，尝试找到第一个<标签
                    int firstTagIndex = xmlContent.IndexOf('<');
                    if (firstTagIndex > 0)
                    {
                        xmlContent = xmlContent.Substring(firstTagIndex);
                    }
                }
                
                System.Diagnostics.Debug.WriteLine($"清理后XML内容：{xmlContent}");
                
                return xmlContent;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清理XML内容失败：{ex.Message}");
                return xmlContent; // 如果清理失败，返回原内容
            }
        }

        /// <summary>
        /// 检测并转换字节数组的编码
        /// </summary>
        /// <param name="bytes">字节数组</param>
        /// <returns>转换后的字符串，如果检测失败返回null</returns>
        private string DetectAndConvertEncoding(byte[] bytes)
        {
            try
            {
                // 检查BOM标记
                if (bytes.Length >= 3 && bytes[0] == 0xEF && bytes[1] == 0xBB && bytes[2] == 0xBF)
                {
                    // UTF-8 BOM
                    return Encoding.UTF8.GetString(bytes, 3, bytes.Length - 3);
                }
                
                if (bytes.Length >= 2)
                {
                    if (bytes[0] == 0xFF && bytes[1] == 0xFE)
                    {
                        // UTF-16 LE BOM
                        return Encoding.Unicode.GetString(bytes, 2, bytes.Length - 2);
                    }
                    if (bytes[0] == 0xFE && bytes[1] == 0xFF)
                    {
                        // UTF-16 BE BOM
                        return Encoding.BigEndianUnicode.GetString(bytes, 2, bytes.Length - 2);
                    }
                }

                // 尝试不同的编码
                var encodings = new[]
                {
                    Encoding.GetEncoding("GBK"),
                    Encoding.GetEncoding("GB2312"),
                    Encoding.UTF8,
                    Encoding.GetEncoding("Big5"),
                    Encoding.Default
                };

                foreach (var encoding in encodings)
                {
                    try
                    {
                        string decoded = encoding.GetString(bytes);
                        
                        // 检查是否包含XML声明，如果有则进一步验证
                        if (decoded.Contains("<?xml"))
                        {
                            // 检查XML声明中的编码
                            var xmlMatch = System.Text.RegularExpressions.Regex.Match(
                                decoded, @"<\?xml[^>]*encoding=['""]([^'""]+)['""]", 
                                System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                            
                            if (xmlMatch.Success)
                            {
                                string declaredEncoding = xmlMatch.Groups[1].Value.ToUpper();
                                if ((declaredEncoding == "GBK" || declaredEncoding == "GB2312") && 
                                    (encoding.WebName.ToUpper() == "GBK" || encoding.WebName.ToUpper() == "GB2312"))
                                {
                                    System.Diagnostics.Debug.WriteLine($"检测到编码：{encoding.WebName}");
                                    return decoded;
                                }
                            }
                        }
                        
                        // 简单的字符验证：检查是否包含大量无效字符
                        int invalidChars = 0;
                        int totalChars = decoded.Length;
                        
                        foreach (char c in decoded)
                        {
                            if (c == '\uFFFD' || (c < 32 && c != '\r' && c != '\n' && c != '\t'))
                            {
                                invalidChars++;
                            }
                        }
                        
                        // 如果无效字符少于5%，认为编码正确
                        if (totalChars > 0 && (double)invalidChars / totalChars < 0.05)
                        {
                            System.Diagnostics.Debug.WriteLine($"检测到编码：{encoding.WebName}");
                            return decoded;
                        }
                    }
                    catch
                    {
                        // 继续尝试下一个编码
                        continue;
                    }
                }
                
                return null; // 检测失败
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"编码检测失败：{ex.Message}");
                return null;
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
} 
# 影像系统入室核对模块

这是一个专为医院影像科设计的患者入室核对系统界面，支持扫码枪输入和手动输入，能够快速核对患者身份信息。

## 文件说明

- `patient_verification.html` - 完整的单文件版本（包含所有CSS和JS）
- `patient_verification_modular.html` - 模块化版本（引用外部CSS和JS文件）
- `styles.css` - 样式文件
- `verification.js` - 核对逻辑JavaScript文件

## 功能特性

### 核心功能
- ✅ 患者基本信息显示
- ✅ 扫码枪输入支持
- ✅ 手动输入支持
- ✅ 实时核对验证
- ✅ 核对结果显示（通过/失败）
- ✅ 自动清空和重新聚焦
- ✅ 实时时间显示
- ✅ 操作历史记录

### 界面特性
- 🎨 现代化扁平设计
- 📱 响应式布局
- 🌙 深色模式支持
- ♿ 高对比度模式支持
- 🔊 音效提示（预留接口）
- ⌨️ 键盘快捷键支持

## 使用方法

### 直接使用
1. 打开 `patient_verification.html` 或 `patient_verification_modular.html`
2. 在扫码输入框中扫描或输入患者编号
3. 系统自动核对或点击"核对"按钮
4. 查看核对结果

### 测试功能
- 点击"测试患者1/2/3"按钮切换不同患者信息
- 输入对应的患者编号进行核对测试
- 默认患者编号：P202401001, P202401002, P202401003

## WPF 复刻指南

### 布局结构
```xml
<Grid>
    <!-- 主容器 -->
    <Border Background="White" CornerRadius="12" Margin="20">
        <Grid Margin="30">
            <!-- 标题区域 -->
            <StackPanel Grid.Row="0" HorizontalAlignment="Center">
                <TextBlock Text="影像系统入室核对" FontSize="28" FontWeight="SemiBold"/>
                <TextBlock Text="Patient Verification System" FontSize="16" Opacity="0.7"/>
            </StackPanel>
            
            <!-- 主要内容区域 -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- 患者信息面板 -->
                <Border Grid.Column="0" Background="#F8F9FA" CornerRadius="8" Padding="20">
                    <StackPanel>
                        <TextBlock Text="📋 患者基本信息" FontSize="18" FontWeight="SemiBold"/>
                        <!-- 患者信息项 -->
                    </StackPanel>
                </Border>
                
                <!-- 扫码核对面板 -->
                <Border Grid.Column="1" Background="#F8F9FA" CornerRadius="8" Padding="20">
                    <StackPanel>
                        <TextBlock Text="🔍 扫码核对" FontSize="18" FontWeight="SemiBold"/>
                        <TextBox Name="ScanInput" FontFamily="Consolas" FontSize="16"/>
                        <StackPanel Orientation="Horizontal">
                            <Button Content="核对" Click="VerifyPatient_Click"/>
                            <Button Content="清空" Click="ClearInput_Click"/>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </Grid>
            
            <!-- 核对结果区域 -->
            <Border Grid.Row="2" Name="ResultPanel" Visibility="Collapsed">
                <!-- 结果显示内容 -->
            </Border>
            
            <!-- 状态栏 -->
            <Border Grid.Row="3" Background="#34495E" CornerRadius="6" Padding="12,8">
                <Grid>
                    <TextBlock Name="TimeDisplay" HorizontalAlignment="Left"/>
                    <TextBlock Text="操作员: 李医生 | 科室: 影像科" HorizontalAlignment="Right"/>
                </Grid>
            </Border>
        </Grid>
    </Border>
</Grid>
```

### 关键样式设置
```xml
<!-- 资源字典 -->
<Window.Resources>
    <!-- 主要颜色 -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="#3498DB"/>
    <SolidColorBrush x:Key="SuccessBrush" Color="#28A745"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="#DC3545"/>
    <SolidColorBrush x:Key="BackgroundBrush" Color="#F8F9FA"/>
    
    <!-- 按钮样式 -->
    <Style x:Key="PrimaryButton" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Padding" Value="20,10"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}" 
                            CornerRadius="6" Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center" 
                                        VerticalAlignment="Center"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 输入框样式 -->
    <Style x:Key="ScanInput" TargetType="TextBox">
        <Setter Property="FontFamily" Value="Consolas"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="Padding" Value="12"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="BorderBrush" Value="#BDC3C7"/>
    </Style>
</Window.Resources>
```

### C# 后台代码示例
```csharp
public partial class PatientVerificationWindow : Window
{
    private PatientInfo currentPatient;
    private DispatcherTimer timeTimer;
    
    public PatientVerificationWindow()
    {
        InitializeComponent();
        InitializeSystem();
    }
    
    private void InitializeSystem()
    {
        // 初始化时间显示
        timeTimer = new DispatcherTimer();
        timeTimer.Interval = TimeSpan.FromSeconds(1);
        timeTimer.Tick += UpdateTime;
        timeTimer.Start();
        
        // 设置焦点到输入框
        ScanInput.Focus();
        
        // 加载患者信息
        LoadPatientInfo();
    }
    
    private void UpdateTime(object sender, EventArgs e)
    {
        TimeDisplay.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
    }
    
    private void ScanInput_KeyDown(object sender, KeyEventArgs e)
    {
        if (e.Key == Key.Enter)
        {
            VerifyPatient();
        }
    }
    
    private void VerifyPatient_Click(object sender, RoutedEventArgs e)
    {
        VerifyPatient();
    }
    
    private void VerifyPatient()
    {
        string scannedId = ScanInput.Text.Trim();
        string expectedId = currentPatient.Id;
        
        if (string.IsNullOrEmpty(scannedId))
        {
            MessageBox.Show("请输入或扫描患者编号！", "提示", 
                          MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }
        
        bool isMatch = scannedId == expectedId;
        ShowVerificationResult(isMatch, scannedId, expectedId);
    }
    
    private void ShowVerificationResult(bool success, string scanned, string expected)
    {
        ResultPanel.Visibility = Visibility.Visible;
        
        if (success)
        {
            // 显示成功结果
            ResultPanel.Background = new SolidColorBrush(Color.FromRgb(212, 237, 218));
            ResultIcon.Text = "✅";
            ResultMessage.Text = "核对通过！患者信息匹配成功";
            
            // 3秒后自动清空
            var timer = new DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(3);
            timer.Tick += (s, e) => {
                ClearInput();
                timer.Stop();
            };
            timer.Start();
        }
        else
        {
            // 显示失败结果
            ResultPanel.Background = new SolidColorBrush(Color.FromRgb(248, 215, 218));
            ResultIcon.Text = "❌";
            ResultMessage.Text = $"核对失败！\n扫描编号: {scanned}\n预期编号: {expected}";
        }
    }
    
    private void ClearInput_Click(object sender, RoutedEventArgs e)
    {
        ClearInput();
    }
    
    private void ClearInput()
    {
        ScanInput.Text = "";
        ResultPanel.Visibility = Visibility.Collapsed;
        ScanInput.Focus();
    }
}
```

## WinForm 复刻指南

### 窗体设计
```csharp
public partial class PatientVerificationForm : Form
{
    private PatientInfo currentPatient;
    private System.Windows.Forms.Timer timeTimer;
    
    public PatientVerificationForm()
    {
        InitializeComponent();
        InitializeSystem();
    }
    
    private void InitializeComponent()
    {
        // 设置窗体属性
        this.Size = new Size(900, 700);
        this.StartPosition = FormStartPosition.CenterScreen;
        this.Text = "影像系统入室核对";
        this.BackColor = Color.FromArgb(245, 247, 250);
        
        // 创建主面板
        var mainPanel = new Panel
        {
            Size = new Size(850, 650),
            Location = new Point(25, 25),
            BackColor = Color.White,
            BorderStyle = BorderStyle.None
        };
        
        // 添加圆角效果（需要自定义绘制）
        mainPanel.Paint += (s, e) => {
            var rect = new Rectangle(0, 0, mainPanel.Width, mainPanel.Height);
            var path = GetRoundedRectPath(rect, 12);
            e.Graphics.FillPath(Brushes.White, path);
        };
        
        this.Controls.Add(mainPanel);
        
        // 创建标题
        var titleLabel = new Label
        {
            Text = "影像系统入室核对",
            Font = new Font("Microsoft YaHei", 18, FontStyle.Bold),
            ForeColor = Color.FromArgb(44, 62, 80),
            Size = new Size(400, 40),
            Location = new Point(225, 30),
            TextAlign = ContentAlignment.MiddleCenter
        };
        mainPanel.Controls.Add(titleLabel);
        
        // 创建患者信息面板
        CreatePatientInfoPanel(mainPanel);
        
        // 创建扫码面板
        CreateScanPanel(mainPanel);
        
        // 创建结果面板
        CreateResultPanel(mainPanel);
        
        // 创建状态栏
        CreateStatusBar(mainPanel);
    }
    
    private void CreatePatientInfoPanel(Panel parent)
    {
        var infoPanel = new Panel
        {
            Size = new Size(380, 300),
            Location = new Point(30, 100),
            BackColor = Color.FromArgb(248, 249, 250),
            BorderStyle = BorderStyle.FixedSingle
        };
        
        // 添加患者信息标签和值
        var labels = new[] { "姓名:", "性别:", "年龄:", "患者编号:", "检查项目:", "预约时间:" };
        var values = new[] { "patientName", "patientGender", "patientAge", 
                           "patientId", "examType", "appointmentTime" };
        
        for (int i = 0; i < labels.Length; i++)
        {
            var labelControl = new Label
            {
                Text = labels[i],
                Font = new Font("Microsoft YaHei", 10, FontStyle.Bold),
                Size = new Size(80, 25),
                Location = new Point(20, 50 + i * 35),
                ForeColor = Color.FromArgb(52, 73, 94)
            };
            
            var valueControl = new Label
            {
                Name = values[i],
                Text = "-",
                Font = new Font("Microsoft YaHei", 10),
                Size = new Size(250, 25),
                Location = new Point(110, 50 + i * 35),
                ForeColor = Color.FromArgb(44, 62, 80),
                TextAlign = ContentAlignment.MiddleRight
            };
            
            infoPanel.Controls.Add(labelControl);
            infoPanel.Controls.Add(valueControl);
        }
        
        parent.Controls.Add(infoPanel);
    }
    
    private void CreateScanPanel(Panel parent)
    {
        var scanPanel = new Panel
        {
            Size = new Size(380, 300),
            Location = new Point(440, 100),
            BackColor = Color.FromArgb(248, 249, 250),
            BorderStyle = BorderStyle.FixedSingle
        };
        
        // 扫码输入框
        var scanInput = new TextBox
        {
            Name = "scanInput",
            Font = new Font("Consolas", 14),
            Size = new Size(340, 30),
            Location = new Point(20, 80),
            BorderStyle = BorderStyle.FixedSingle
        };
        scanInput.KeyPress += ScanInput_KeyPress;
        
        // 核对按钮
        var verifyButton = new Button
        {
            Text = "核对",
            Font = new Font("Microsoft YaHei", 12, FontStyle.Bold),
            Size = new Size(160, 40),
            Location = new Point(20, 130),
            BackColor = Color.FromArgb(52, 152, 219),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat
        };
        verifyButton.Click += VerifyButton_Click;
        
        // 清空按钮
        var clearButton = new Button
        {
            Text = "清空",
            Font = new Font("Microsoft YaHei", 12, FontStyle.Bold),
            Size = new Size(160, 40),
            Location = new Point(200, 130),
            BackColor = Color.FromArgb(149, 165, 166),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat
        };
        clearButton.Click += ClearButton_Click;
        
        scanPanel.Controls.AddRange(new Control[] { scanInput, verifyButton, clearButton });
        parent.Controls.Add(scanPanel);
    }
}
```

## 扫码枪集成要点

### 1. 输入处理
- 扫码枪通常模拟键盘输入
- 输入完成后自动发送回车键
- 需要保持输入框始终获得焦点

### 2. 自动触发
- 监听输入长度达到预期值时自动核对
- 设置适当的延迟避免输入未完成就触发

### 3. 字符过滤
- 清理可能的特殊字符
- 统一大小写处理

## 数据库集成建议

### 患者信息表结构
```sql
CREATE TABLE Patients (
    Id NVARCHAR(20) PRIMARY KEY,
    Name NVARCHAR(50) NOT NULL,
    Gender NVARCHAR(10),
    Age INT,
    ExamType NVARCHAR(100),
    AppointmentTime DATETIME,
    Status NVARCHAR(20) DEFAULT 'Scheduled'
);
```

### 核对记录表结构
```sql
CREATE TABLE VerificationLogs (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    PatientId NVARCHAR(20),
    ScannedId NVARCHAR(20),
    IsSuccess BIT,
    VerificationTime DATETIME DEFAULT GETDATE(),
    OperatorName NVARCHAR(50),
    WorkstationId NVARCHAR(20)
);
```

## 部署建议

1. **单机版本**: 直接打开HTML文件使用
2. **网络版本**: 部署到IIS或其他Web服务器
3. **桌面应用**: 使用WebView控件嵌入到WPF/WinForm中
4. **混合应用**: 使用Electron等技术打包成桌面应用

## 扩展功能

- [ ] 语音播报核对结果
- [ ] 打印核对凭证
- [ ] 批量核对模式
- [ ] 统计报表功能
- [ ] 多语言支持
- [ ] 主题切换功能

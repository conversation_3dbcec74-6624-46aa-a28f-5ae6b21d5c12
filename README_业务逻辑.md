# 患者入室核对系统 - 业务逻辑说明

## 功能概述

本系统实现了完整的患者入室核对业务逻辑，包括：

1. **命令行参数解析**：支持传入用户ID和检查流水号
2. **数据库查询**：从数据库获取患者信息
3. **核对逻辑**：根据配置进行不同的核对方式
4. **状态管理**：防止重复核对，记录核对信息

## 使用方法

### 命令行启动
```
CheckPatientInfo.exe 30B43B10C47246A6B49A6609BE32A12B#20250730002634
```

参数格式：`用户ID#检查流水号`

### 配置文件
需要在程序目录下放置 `Connection.ini` 文件：
```ini
[Connection]
Url=http://10.0.252.111:8090/PacsDBProxy/PacsDatabase/PacsDatabase.action
```

## 核对逻辑

### 1. 基础核对（ConfigValue != "1"）
- 直接比较扫码输入的患者编号与数据库中的患者编号
- 一致则核对通过

### 2. 高级核对（ConfigValue == "1"）
- 首先比较扫码输入的患者编号与数据库中的患者编号
- 然后查询队列信息，比较患者姓名和性别
- 所有信息都匹配才算核对通过

## 核对状态管理

### 未核对状态
- 输入框可用
- 核对和清空按钮可用
- 状态栏显示"状态：未核对"

### 已核对状态
- 输入框禁用
- 核对和清空按钮禁用
- 状态栏显示核对人和核对时间
- 不支持二次核对

## 数据库操作

### 查询患者信息
```sql
select 
h.infotype||d.devicetypename as typename,
p.patientname,
p.sex,
t.age||t.ageunit as age,
case when p.hispatienttype=1 then 
  p.clinicpatientid 
when p.hispatienttype=2 then
  p.infeepatientid
else p.clinicpatientid end  as patientid,
  t.studyid,
 m.departmentname,
 t.studyscription,
 (select u.username from pacsuser u where u.userid= t.checkverifyid) as checkverifyid,
 t.checkverifytime,
 (select e.paramvalue from parameter e where e.paramname='CheckVerifyConfig') as configvalue,
 (select u.username from pacsuser u where u.userid='用户ID') Currentuser
  from studyinfo t
  left join patientinfo p
    on p.checkserialnum = t.checkserialnum
  left join hisinfotype h on h.infotypeid=p.hispatienttype
  left join devicetypeinfo d on d.devicetypeid=t.devicetypeid
  left join pacsdepartment m on m.departmentid=t.departmentid
  where t.checkserialnum='检查流水号';
```

### 查询队列信息（当ConfigValue=1时）
```sql
select a.patient_name     as patientname,
       a.patient_sex_desc as sex,
       a.business_id      as studyid
  from v_current_queueinfo a
 where  trunc(a.queue_date) = trunc(sysdate)
 and a.queue_status=3
 and a.business_id='检查号';
```

### 更新核对信息
```sql
update studyinfo 
set checkverifyid='用户ID', checkverifytime=sysdate 
where checkserialnum='检查流水号';
```

## 错误处理

1. **配置文件不存在**：提示用户配置文件缺失
2. **数据库连接失败**：显示连接错误信息
3. **患者信息不存在**：提示未找到对应患者
4. **核对失败**：显示具体的失败原因
5. **更新失败**：提示保存失败但核对成功

## 技术特点

- 兼容 .NET Framework 4.5.2
- 异步数据库操作，避免界面卡顿
- 完整的错误处理和用户提示
- 支持GBK编码的数据库响应
- 防止重复核对的状态管理

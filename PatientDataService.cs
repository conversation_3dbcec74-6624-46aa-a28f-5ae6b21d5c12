using System;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml;

namespace CheckPatientInfo
{
    /// <summary>
    /// 患者数据服务类
    /// </summary>
    public class PatientDataService : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly string _connectionUrl;

        public PatientDataService()
        {
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(30);
            _connectionUrl = ConfigHelper.GetConnectionUrl();
            System.Diagnostics.Debug.WriteLine($"数据库连接URL：{_connectionUrl}");
        }

        /// <summary>
        /// 获取患者检查信息
        /// </summary>
        /// <param name="checkSerialNum">检查流水号</param>
        /// <param name="userId">用户ID</param>
        /// <returns>患者检查信息</returns>
        public async Task<PatientCheckInfo> GetPatientCheckInfoAsync(string checkSerialNum, string userId)
        {
            // 先测试简单查询，确认数据是否存在
            string testSql = $"select count(*) as cnt from studyinfo where checkserialnum='{checkSerialNum}'";
            System.Diagnostics.Debug.WriteLine($"测试SQL：{testSql}");

            try
            {
                string testResponse = await ExecuteQueryAsync(testSql);
                System.Diagnostics.Debug.WriteLine($"测试查询响应：{testResponse}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"测试查询失败：{ex.Message}");
            }

            string sql = $@"
                select
                h.infotype||d.devicetypename as typename,
                p.patientname,
                p.sex,
                t.age||t.ageunit as age,
                case when p.hispatienttype=1 then
                  p.clinicpatientid
                when p.hispatienttype=2 then
                  p.infeepatientid
                else p.clinicpatientid end  as patientid,
                  t.studyid,
                 m.departmentname,
                 t.studyscription,
                 (select u.username from pacsuser u where u.userid= t.checkverifyid) as checkverifyid,
                 t.checkverifytime,
                 (select e.paramvalue from parameter e where e.paramname='CheckVerifyConfig') as configvalue,
                 (select u.username from pacsuser u where u.userid='{userId}') Currentuser
                  from studyinfo t
                  left join patientinfo p
                    on p.checkserialnum = t.checkserialnum
                  left join hisinfotype h on h.infotypeid=p.hispatienttype
                  left join devicetypeinfo d on d.devicetypeid=t.devicetypeid
                  left join pacsdepartment m on m.departmentid=t.departmentid
                  where t.checkserialnum='{checkSerialNum}'";

            System.Diagnostics.Debug.WriteLine($"执行完整SQL查询：{sql}");
            System.Diagnostics.Debug.WriteLine($"查询参数 - 检查流水号：{checkSerialNum}, 用户ID：{userId}");

            string response = await ExecuteQueryAsync(sql);
            System.Diagnostics.Debug.WriteLine($"数据库响应：{response}");

            return ParsePatientCheckInfo(response);
        }

        /// <summary>
        /// 获取队列信息
        /// </summary>
        /// <param name="studyId">检查号</param>
        /// <returns>队列信息</returns>
        public async Task<QueueInfo> GetQueueInfoAsync(string studyId)
        {
            string sql = $@"
                select a.patient_name     as patientname,
                       a.patient_sex_desc as sex,
                       a.business_id      as studyid
                  from v_current_queueinfo a
                 where  trunc(a.queue_date) = trunc(sysdate)
                 and a.queue_status=3
                 and a.business_id='{studyId}'";

            string response = await ExecuteQueryAsync(sql);
            return ParseQueueInfo(response);
        }

        /// <summary>
        /// 更新核对信息
        /// </summary>
        /// <param name="checkSerialNum">检查流水号</param>
        /// <param name="userId">用户ID</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> UpdateVerificationAsync(string checkSerialNum, string userId)
        {
            string sql = $@"
                update studyinfo 
                set checkverifyid='{userId}', checkverifytime=sysdate 
                where checkserialnum='{checkSerialNum}'";

            try
            {
                string response = await ExecuteUpdateAsync(sql);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private async Task<string> ExecuteQueryAsync(string sql)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"准备执行数据库查询，连接URL：{_connectionUrl}");

                string xmlRequest = CreateQueryXml(sql);
                System.Diagnostics.Debug.WriteLine($"发送的XML请求：{xmlRequest}");

                var content = new StringContent(xmlRequest, Encoding.UTF8, "application/xml");

                HttpResponseMessage response = await _httpClient.PostAsync(_connectionUrl, content);

                System.Diagnostics.Debug.WriteLine($"HTTP响应状态：{response.StatusCode}");
                System.Diagnostics.Debug.WriteLine($"HTTP响应头：{response.Headers}");

                byte[] responseBytes = await response.Content.ReadAsByteArrayAsync();
                string responseContent = ConvertGbkToUtf8(responseBytes);

                System.Diagnostics.Debug.WriteLine($"HTTP响应内容：{responseContent}");

                if (IsErrorResponse(responseContent))
                {
                    string errorMsg = ExtractErrorMessage(responseContent);
                    System.Diagnostics.Debug.WriteLine($"检测到错误响应：{errorMsg}");
                    throw new Exception($"服务器返回错误：{errorMsg}");
                }

                return responseContent;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"数据库查询异常：{ex.Message}");
                throw new Exception($"数据库查询失败: {ex.Message}");
            }
        }

        private async Task<string> ExecuteUpdateAsync(string sql)
        {
            try
            {
                string xmlRequest = CreateUpdateXml(sql);
                var content = new StringContent(xmlRequest, Encoding.UTF8, "application/xml");
                
                HttpResponseMessage response = await _httpClient.PostAsync(_connectionUrl, content);
                
                byte[] responseBytes = await response.Content.ReadAsByteArrayAsync();
                return ConvertGbkToUtf8(responseBytes);
            }
            catch (Exception ex)
            {
                throw new Exception($"数据库更新失败: {ex.Message}");
            }
        }

        private string CreateQueryXml(string sql)
        {
            return $@"<request>
    <sqltype>query</sqltype>
    <offset>0</offset>
    <limit>10</limit>
    <statements>
        <statement name=""query1"">
            <sql><![CDATA[{sql}]]></sql>
            <input>
            </input>
            <out>
            </out>
        </statement>
    </statements>
</request>";
        }

        private string CreateUpdateXml(string sql)
        {
            return $@"<request>
    <sqltype>update</sqltype>
    <offset>0</offset>
    <limit>10</limit>
    <statements>
        <statement name=""update1"">
            <sql><![CDATA[{sql}]]></sql>
            <input>
            </input>
            <out>
            </out>
        </statement>
    </statements>
</request>";
        }

        private string ConvertGbkToUtf8(byte[] gbkBytes)
        {
            try
            {
                Encoding gbkEncoding = Encoding.GetEncoding("GBK");
                return gbkEncoding.GetString(gbkBytes);
            }
            catch
            {
                return Encoding.UTF8.GetString(gbkBytes);
            }
        }

        private bool IsErrorResponse(string responseContent)
        {
            if (string.IsNullOrEmpty(responseContent))
                return false;

            string content = responseContent.ToLower();
            return content.Contains("<html") || 
                   content.Contains("http status") || 
                   content.Contains("error") && content.Contains("<title>");
        }

        private string ExtractErrorMessage(string errorResponse)
        {
            try
            {
                var titleMatch = Regex.Match(errorResponse, @"<title[^>]*>([^<]+)</title>", RegexOptions.IgnoreCase);
                if (titleMatch.Success)
                {
                    return titleMatch.Groups[1].Value.Trim();
                }
                return "服务器返回错误";
            }
            catch
            {
                return "解析错误信息失败";
            }
        }

        private PatientCheckInfo ParsePatientCheckInfo(string xmlResponse)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始解析XML响应，长度：{xmlResponse?.Length ?? 0}");

                if (string.IsNullOrWhiteSpace(xmlResponse))
                {
                    System.Diagnostics.Debug.WriteLine("XML响应为空");
                    return null;
                }

                XmlDocument doc = new XmlDocument();
                doc.LoadXml(xmlResponse);

                // 根据接口文档，查找 rs:data/z:row 节点
                XmlNamespaceManager nsmgr = new XmlNamespaceManager(doc.NameTable);
                nsmgr.AddNamespace("rs", "urn:schemas-microsoft-com:rowset");
                nsmgr.AddNamespace("z", "#RowsetSchema");

                XmlNode rowNode = doc.SelectSingleNode("//rs:data/z:row", nsmgr);
                if (rowNode == null)
                {
                    System.Diagnostics.Debug.WriteLine("未找到rs:data/z:row节点，可能没有查询到数据");

                    // 输出XML结构用于调试
                    System.Diagnostics.Debug.WriteLine("XML结构分析：");
                    if (doc.DocumentElement != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"根节点：{doc.DocumentElement.Name}");
                        foreach (XmlNode node in doc.DocumentElement.ChildNodes)
                        {
                            System.Diagnostics.Debug.WriteLine($"子节点：{node.Name}");
                        }
                    }

                    return null;
                }

                System.Diagnostics.Debug.WriteLine($"找到数据行，属性数量：{rowNode.Attributes?.Count ?? 0}");

                var info = new PatientCheckInfo();

                // 根据接口文档，字段作为属性存储在z:row节点中
                if (rowNode.Attributes != null)
                {
                    foreach (XmlAttribute attr in rowNode.Attributes)
                    {
                        string value = attr.Value?.Trim();
                        string fieldName = attr.Name.ToUpper(); // 属性名通常是大写

                        System.Diagnostics.Debug.WriteLine($"属性：{fieldName} = {value}");

                        switch (fieldName)
                        {
                            case "TYPENAME":
                                info.TypeName = value;
                                break;
                            case "PATIENTNAME":
                                info.PatientName = value;
                                break;
                            case "SEX":
                                info.Sex = value;
                                break;
                            case "AGE":
                                info.Age = value;
                                break;
                            case "PATIENTID":
                                info.PatientId = value;
                                break;
                            case "STUDYID":
                                info.StudyId = value;
                                break;
                            case "DEPARTMENTNAME":
                                info.DepartmentName = value;
                                break;
                            case "STUDYSCRIPTION":
                                info.StudyScription = value;
                                break;
                            case "CHECKVERIFYID":
                                info.CheckVerifyId = value;
                                break;
                            case "CHECKVERIFYTIME":
                                if (!string.IsNullOrEmpty(value) && DateTime.TryParse(value, out DateTime verifyTime))
                                {
                                    info.CheckVerifyTime = verifyTime;
                                }
                                break;
                            case "CONFIGVALUE":
                                info.ConfigValue = value;
                                break;
                            case "CURRENTUSER":
                                info.CurrentUser = value;
                                break;
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"解析完成，患者姓名：{info.PatientName}");
                return info;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解析XML失败：{ex.Message}");
                System.Diagnostics.Debug.WriteLine($"XML内容：{xmlResponse}");
                throw new Exception($"解析患者信息失败：{ex.Message}");
            }
        }

        private QueueInfo ParseQueueInfo(string xmlResponse)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始解析队列信息XML响应");

                if (string.IsNullOrWhiteSpace(xmlResponse))
                {
                    return null;
                }

                XmlDocument doc = new XmlDocument();
                doc.LoadXml(xmlResponse);

                // 使用Microsoft Rowset格式解析
                XmlNamespaceManager nsmgr = new XmlNamespaceManager(doc.NameTable);
                nsmgr.AddNamespace("rs", "urn:schemas-microsoft-com:rowset");
                nsmgr.AddNamespace("z", "#RowsetSchema");

                XmlNode rowNode = doc.SelectSingleNode("//rs:data/z:row", nsmgr);
                if (rowNode == null)
                {
                    System.Diagnostics.Debug.WriteLine("队列信息：未找到rs:data/z:row节点");
                    return null;
                }

                var info = new QueueInfo();

                if (rowNode.Attributes != null)
                {
                    foreach (XmlAttribute attr in rowNode.Attributes)
                    {
                        string value = attr.Value?.Trim();
                        string fieldName = attr.Name.ToUpper();

                        System.Diagnostics.Debug.WriteLine($"队列信息属性：{fieldName} = {value}");

                        switch (fieldName)
                        {
                            case "PATIENTNAME":
                                info.PatientName = value;
                                break;
                            case "SEX":
                                info.Sex = value;
                                break;
                            case "STUDYID":
                                info.StudyId = value;
                                break;
                        }
                    }
                }

                return info;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解析队列信息失败：{ex.Message}");
                throw new Exception($"解析队列信息失败：{ex.Message}");
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}

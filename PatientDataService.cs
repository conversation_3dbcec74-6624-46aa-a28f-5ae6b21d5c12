using System;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml;

namespace CheckPatientInfo
{
    /// <summary>
    /// 患者数据服务类
    /// </summary>
    public class PatientDataService : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly string _connectionUrl;

        public PatientDataService()
        {
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(30);
            _connectionUrl = ConfigHelper.GetConnectionUrl();
            System.Diagnostics.Debug.WriteLine($"数据库连接URL：{_connectionUrl}");
        }

        /// <summary>
        /// 获取患者检查信息
        /// </summary>
        /// <param name="checkSerialNum">检查流水号</param>
        /// <param name="userId">用户ID</param>
        /// <returns>患者检查信息</returns>
        public async Task<PatientCheckInfo> GetPatientCheckInfoAsync(string checkSerialNum, string userId)
        {
            // 先测试简单查询，确认数据是否存在
            string testSql = $"select count(*) as cnt from studyinfo where checkserialnum='{checkSerialNum}'";
            System.Diagnostics.Debug.WriteLine($"测试SQL：{testSql}");

            try
            {
                string testResponse = await ExecuteQueryAsync(testSql);
                System.Diagnostics.Debug.WriteLine($"测试查询响应：{testResponse}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"测试查询失败：{ex.Message}");
            }

            string sql = $@"
                select
                h.infotype||d.devicetypename as typename,
                p.patientname,
                p.sex,
                t.age||t.ageunit as age,
                case when p.hispatienttype=1 then
                  p.clinicpatientid
                when p.hispatienttype=2 then
                  p.infeepatientid
                else p.clinicpatientid end  as patientid,
                  t.studyid,
                 m.departmentname,
                 t.studyscription,
                 (select u.username from pacsuser u where u.userid= t.checkverifyid) as checkverifyid,
                 t.checkverifytime,
                 (select e.paramvalue from parameter e where e.paramname='CheckVerifyConfig') as configvalue,
                 (select u.username from pacsuser u where u.userid='{userId}') Currentuser
                  from studyinfo t
                  left join patientinfo p
                    on p.checkserialnum = t.checkserialnum
                  left join hisinfotype h on h.infotypeid=p.hispatienttype
                  left join devicetypeinfo d on d.devicetypeid=t.devicetypeid
                  left join pacsdepartment m on m.departmentid=t.departmentid
                  where t.checkserialnum='{checkSerialNum}'";

            System.Diagnostics.Debug.WriteLine($"执行完整SQL查询：{sql}");
            System.Diagnostics.Debug.WriteLine($"查询参数 - 检查流水号：{checkSerialNum}, 用户ID：{userId}");

            string response = await ExecuteQueryAsync(sql);
            System.Diagnostics.Debug.WriteLine($"数据库响应：{response}");

            return ParsePatientCheckInfo(response);
        }

        /// <summary>
        /// 获取队列信息
        /// </summary>
        /// <param name="studyId">检查号</param>
        /// <returns>队列信息</returns>
        public async Task<QueueInfo> GetQueueInfoAsync(string studyId)
        {
            string sql = $@"
                select a.patient_name     as patientname,
                       a.patient_sex_desc as sex,
                       a.business_id      as studyid
                  from v_current_queueinfo a
                 where  trunc(a.queue_date) = trunc(sysdate)
                 and a.queue_status=3
                 and a.business_id='{studyId}'";

            string response = await ExecuteQueryAsync(sql);
            return ParseQueueInfo(response);
        }

        /// <summary>
        /// 更新核对信息
        /// </summary>
        /// <param name="checkSerialNum">检查流水号</param>
        /// <param name="userId">用户ID</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> UpdateVerificationAsync(string checkSerialNum, string userId)
        {
            string sql = $@"
                update studyinfo 
                set checkverifyid='{userId}', checkverifytime=sysdate 
                where checkserialnum='{checkSerialNum}'";

            try
            {
                string response = await ExecuteUpdateAsync(sql);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private async Task<string> ExecuteQueryAsync(string sql)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"准备执行数据库查询，连接URL：{_connectionUrl}");

                string xmlRequest = CreateQueryXml(sql);
                System.Diagnostics.Debug.WriteLine($"发送的XML请求：{xmlRequest}");

                var content = new StringContent(xmlRequest, Encoding.UTF8, "application/xml");

                HttpResponseMessage response = await _httpClient.PostAsync(_connectionUrl, content);

                System.Diagnostics.Debug.WriteLine($"HTTP响应状态：{response.StatusCode}");
                System.Diagnostics.Debug.WriteLine($"HTTP响应头：{response.Headers}");

                byte[] responseBytes = await response.Content.ReadAsByteArrayAsync();
                string responseContent = ConvertGbkToUtf8(responseBytes);

                System.Diagnostics.Debug.WriteLine($"HTTP响应内容：{responseContent}");

                if (IsErrorResponse(responseContent))
                {
                    string errorMsg = ExtractErrorMessage(responseContent);
                    System.Diagnostics.Debug.WriteLine($"检测到错误响应：{errorMsg}");
                    throw new Exception($"服务器返回错误：{errorMsg}");
                }

                return responseContent;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"数据库查询异常：{ex.Message}");
                throw new Exception($"数据库查询失败: {ex.Message}");
            }
        }

        private async Task<string> ExecuteUpdateAsync(string sql)
        {
            try
            {
                string xmlRequest = CreateUpdateXml(sql);
                var content = new StringContent(xmlRequest, Encoding.UTF8, "application/xml");
                
                HttpResponseMessage response = await _httpClient.PostAsync(_connectionUrl, content);
                
                byte[] responseBytes = await response.Content.ReadAsByteArrayAsync();
                return ConvertGbkToUtf8(responseBytes);
            }
            catch (Exception ex)
            {
                throw new Exception($"数据库更新失败: {ex.Message}");
            }
        }

        private string CreateQueryXml(string sql)
        {
            return $@"<request>
    <sqltype>query</sqltype>
    <offset>0</offset>
    <limit>10</limit>
    <statements>
        <statement name=""query1"">
            <sql><![CDATA[{sql}]]></sql>
            <input>
            </input>
            <out>
            </out>
        </statement>
    </statements>
</request>";
        }

        private string CreateUpdateXml(string sql)
        {
            return $@"<request>
    <sqltype>update</sqltype>
    <offset>0</offset>
    <limit>10</limit>
    <statements>
        <statement name=""update1"">
            <sql><![CDATA[{sql}]]></sql>
            <input>
            </input>
            <out>
            </out>
        </statement>
    </statements>
</request>";
        }

        private string ConvertGbkToUtf8(byte[] gbkBytes)
        {
            try
            {
                Encoding gbkEncoding = Encoding.GetEncoding("GBK");
                return gbkEncoding.GetString(gbkBytes);
            }
            catch
            {
                return Encoding.UTF8.GetString(gbkBytes);
            }
        }

        private bool IsErrorResponse(string responseContent)
        {
            if (string.IsNullOrEmpty(responseContent))
                return false;

            string content = responseContent.ToLower();
            return content.Contains("<html") || 
                   content.Contains("http status") || 
                   content.Contains("error") && content.Contains("<title>");
        }

        private string ExtractErrorMessage(string errorResponse)
        {
            try
            {
                var titleMatch = Regex.Match(errorResponse, @"<title[^>]*>([^<]+)</title>", RegexOptions.IgnoreCase);
                if (titleMatch.Success)
                {
                    return titleMatch.Groups[1].Value.Trim();
                }
                return "服务器返回错误";
            }
            catch
            {
                return "解析错误信息失败";
            }
        }

        private PatientCheckInfo ParsePatientCheckInfo(string xmlResponse)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始解析XML响应，长度：{xmlResponse?.Length ?? 0}");

                if (string.IsNullOrWhiteSpace(xmlResponse))
                {
                    System.Diagnostics.Debug.WriteLine("XML响应为空");
                    return null;
                }

                XmlDocument doc = new XmlDocument();
                doc.LoadXml(xmlResponse);

                // 检查是否有数据节点
                XmlNode dataNode = doc.SelectSingleNode("//data");
                if (dataNode == null)
                {
                    System.Diagnostics.Debug.WriteLine("未找到data节点");
                    return null;
                }

                XmlNode rowNode = dataNode.SelectSingleNode("row");
                if (rowNode == null)
                {
                    System.Diagnostics.Debug.WriteLine("未找到row节点，可能没有查询到数据");
                    // 检查是否有错误信息
                    XmlNode errorNode = doc.SelectSingleNode("//error");
                    if (errorNode != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"数据库返回错误：{errorNode.InnerText}");
                        throw new Exception($"数据库查询错误：{errorNode.InnerText}");
                    }
                    return null;
                }

                System.Diagnostics.Debug.WriteLine($"找到数据行，子节点数量：{rowNode.ChildNodes.Count}");

                var info = new PatientCheckInfo();

                foreach (XmlNode child in rowNode.ChildNodes)
                {
                    string value = child.InnerText?.Trim();
                    string fieldName = child.Name.ToLower();

                    System.Diagnostics.Debug.WriteLine($"字段：{fieldName} = {value}");

                    switch (fieldName)
                    {
                        case "typename":
                            info.TypeName = value;
                            break;
                        case "patientname":
                            info.PatientName = value;
                            break;
                        case "sex":
                            info.Sex = value;
                            break;
                        case "age":
                            info.Age = value;
                            break;
                        case "patientid":
                            info.PatientId = value;
                            break;
                        case "studyid":
                            info.StudyId = value;
                            break;
                        case "departmentname":
                            info.DepartmentName = value;
                            break;
                        case "studyscription":
                            info.StudyScription = value;
                            break;
                        case "checkverifyid":
                            info.CheckVerifyId = value;
                            break;
                        case "checkverifytime":
                            if (!string.IsNullOrEmpty(value) && DateTime.TryParse(value, out DateTime verifyTime))
                            {
                                info.CheckVerifyTime = verifyTime;
                            }
                            break;
                        case "configvalue":
                            info.ConfigValue = value;
                            break;
                        case "currentuser":
                            info.CurrentUser = value;
                            break;
                    }
                }

                System.Diagnostics.Debug.WriteLine($"解析完成，患者姓名：{info.PatientName}");
                return info;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解析XML失败：{ex.Message}");
                System.Diagnostics.Debug.WriteLine($"XML内容：{xmlResponse}");
                throw new Exception($"解析患者信息失败：{ex.Message}");
            }
        }

        private QueueInfo ParseQueueInfo(string xmlResponse)
        {
            try
            {
                XmlDocument doc = new XmlDocument();
                doc.LoadXml(xmlResponse);
                
                XmlNode dataNode = doc.SelectSingleNode("//data/row");
                if (dataNode == null)
                {
                    return null;
                }

                var info = new QueueInfo();
                
                foreach (XmlNode child in dataNode.ChildNodes)
                {
                    string value = child.InnerText?.Trim();
                    
                    switch (child.Name.ToLower())
                    {
                        case "patientname":
                            info.PatientName = value;
                            break;
                        case "sex":
                            info.Sex = value;
                            break;
                        case "studyid":
                            info.StudyId = value;
                            break;
                    }
                }
                
                return info;
            }
            catch (Exception ex)
            {
                throw new Exception($"解析队列信息失败：{ex.Message}");
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}

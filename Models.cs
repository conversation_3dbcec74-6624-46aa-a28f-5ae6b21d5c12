using System;

namespace CheckPatientInfo
{
    /// <summary>
    /// 患者检查信息模型
    /// </summary>
    public class PatientCheckInfo
    {
        /// <summary>
        /// 患者信息类型（门诊CT、门诊DR等）
        /// </summary>
        public string TypeName { get; set; }

        /// <summary>
        /// 患者姓名
        /// </summary>
        public string PatientName { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public string Sex { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public string Age { get; set; }

        /// <summary>
        /// 患者编号
        /// </summary>
        public string PatientId { get; set; }

        /// <summary>
        /// 检查号
        /// </summary>
        public string StudyId { get; set; }

        /// <summary>
        /// 开单科室
        /// </summary>
        public string DepartmentName { get; set; }

        /// <summary>
        /// 检查项目
        /// </summary>
        public string StudyScription { get; set; }

        /// <summary>
        /// 入室核对人
        /// </summary>
        public string CheckVerifyId { get; set; }

        /// <summary>
        /// 入室核对时间
        /// </summary>
        public DateTime? CheckVerifyTime { get; set; }

        /// <summary>
        /// 参数设置值
        /// </summary>
        public string ConfigValue { get; set; }

        /// <summary>
        /// 当前登录用户
        /// </summary>
        public string CurrentUser { get; set; }
    }

    /// <summary>
    /// 队列信息模型
    /// </summary>
    public class QueueInfo
    {
        /// <summary>
        /// 患者姓名
        /// </summary>
        public string PatientName { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public string Sex { get; set; }

        /// <summary>
        /// 检查号
        /// </summary>
        public string StudyId { get; set; }
    }
}

@echo off
echo 正在编译影像系统入室核对程序...
echo.

REM 设置MSBuild路径（根据不同的Visual Studio版本调整）
set MSBUILD_PATH=""

REM 尝试找到MSBuild
if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2017\Professional\MSBuild\15.0\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2017\Professional\MSBuild\15.0\Bin\MSBuild.exe"
) else if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2017\Community\MSBuild\15.0\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2017\Community\MSBuild\15.0\Bin\MSBuild.exe"
) else if exist "%ProgramFiles(x86)%\MSBuild\14.0\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\MSBuild\14.0\Bin\MSBuild.exe"
) else (
    echo 未找到MSBuild，请确保已安装Visual Studio或.NET Framework SDK
    echo 尝试使用系统PATH中的msbuild...
    set MSBUILD_PATH=msbuild
)

echo 使用MSBuild路径: %MSBUILD_PATH%
echo.

REM 编译项目
%MSBUILD_PATH% CheckPatientInfo.sln /p:Configuration=Debug /p:Platform="Any CPU"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo 编译成功！正在启动程序...
    echo.
    start "" "bin\Debug\CheckPatientInfo.exe"
) else (
    echo.
    echo 编译失败，请检查错误信息。
    echo.
    pause
)
